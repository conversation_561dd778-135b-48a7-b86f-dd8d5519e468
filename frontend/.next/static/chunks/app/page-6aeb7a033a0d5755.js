(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{4526:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ee});var a=s(5155),r=s(2115),l=s(9397),n=s(5028);let i=(0,r.createContext)({isConnected:!1,progress:0,chunkResults:[],error:null}),c=e=>{let{children:t}=e,[s,l]=(0,r.useState)(!1),[n,c]=(0,r.useState)(0),[d,o]=(0,r.useState)([]),[m,x]=(0,r.useState)(null),h=(0,r.useRef)(null);return(0,r.useEffect)(()=>{let e=new WebSocket("ws://localhost:8000/ws");return h.current=e,e.onopen=()=>{console.log("WebSocket connected"),l(!0),x(null)},e.onmessage=e=>{let t=JSON.parse(e.data);switch(console.log("WebSocket message:",t),t.type){case"status":console.log("Status:",t.message);break;case"preview":console.log("Preview data received:",t.data);break;case"chunk":o(e=>[...e,t.data]),c(t.data.progress);break;case"complete":console.log("Analysis complete:",t.data),c(100);break;case"error":x(t.message),console.error("WebSocket error:",t)}},e.onerror=e=>{console.error("WebSocket error:",e),x("Connection error"),l(!1)},e.onclose=()=>{console.log("WebSocket disconnected"),l(!1)},()=>{e.readyState===WebSocket.OPEN&&e.close()}},[]),(0,a.jsx)(i.Provider,{value:{isConnected:s,progress:n,chunkResults:d,error:m},children:t})};var d=s(6517),o=s(1243),m=s(4449),x=s(2596);let h=e=>{let{isConnected:t,progress:s=0,dataPoints:r=0,error:n,compact:i=!1}=e;return i?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:(0,x.A)("status-dot",t?"connected":"disconnected")}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:t?"Connected":"Disconnected"}),s>0&&s<100&&(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["• ",Math.round(s),"%"]})]}):(0,a.jsxs)("div",{className:"glass-card p-4 animate-fadeIn",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"p-2 rounded bg-gray-50",children:t?(0,a.jsx)(d.A,{className:"w-5 h-5 text-black"}):n?(0,a.jsx)(o.A,{className:"w-5 h-5 text-red-600"}):(0,a.jsx)(m.A,{className:"w-5 h-5 text-gray-400"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-900",children:"Connection Status"}),(0,a.jsx)("div",{className:(0,x.A)("status-dot",t?"connected":"disconnected")})]}),(0,a.jsx)("p",{className:(0,x.A)("text-sm",t?"text-gray-700":n?"text-red-600":"text-gray-500"),children:t?"WebSocket connected":n?"Connection error":"Waiting for connection..."}),n&&(0,a.jsx)("p",{className:"text-xs text-red-500 mt-1",children:n})]})]}),t&&r>0&&(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 text-gray-700",children:[(0,a.jsx)(l.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm font-semibold",children:r.toLocaleString()})]}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"data points"})]})]}),t&&s>0&&s<100&&(0,a.jsxs)("div",{className:"mt-3 pt-3 border-t border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"Processing"}),(0,a.jsxs)("span",{className:"text-xs font-semibold text-gray-700",children:[Math.round(s),"%"]})]}),(0,a.jsx)("div",{className:"progress-bar",children:(0,a.jsx)("div",{className:"progress-fill",style:{width:"".concat(s,"%")}})})]})]})};var u=s(646);let g=e=>{let{progress:t,label:s="Processing",showPercentage:r=!0,size:n="md"}=e,i=t>=100;return(0,a.jsxs)("div",{className:"w-full",children:[(s||r)&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[i?(0,a.jsx)(u.A,{className:"w-4 h-4 text-black"}):(0,a.jsx)(l.A,{className:"w-4 h-4 text-gray-600 pulse"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:s})]}),r&&(0,a.jsxs)("span",{className:(0,x.A)("text-sm font-semibold",i?"text-black":"text-gray-600"),children:[Math.round(t),"%"]})]}),(0,a.jsx)("div",{className:(0,x.A)("w-full bg-gray-200 rounded overflow-hidden",{sm:"h-1",md:"h-1.5",lg:"h-2"}[n]),children:(0,a.jsx)("div",{className:"h-full bg-black rounded transition-all duration-500 ease-out",style:{width:"".concat(Math.min(100,Math.max(0,t)),"%")}})}),i&&(0,a.jsxs)("div",{className:"mt-2 flex items-center gap-2 animate-slideIn",children:[(0,a.jsx)(u.A,{className:"w-3.5 h-3.5 text-black"}),(0,a.jsx)("span",{className:"text-xs text-black font-medium",children:"Complete"})]})]})};var p=s(4498),f=s(2355),y=s(3052),j=s(4481),b=s(6262),N=s(1788);let v=(0,n.default)(()=>Promise.all([s.e(840),s.e(292)]).then(s.bind(s,284)),{loadableGenerated:{webpack:()=>[284]},ssr:!1,loading:()=>(0,a.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsx)(p.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"})}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Loading visualization..."})]})})}),w=()=>{let{isConnected:e,progress:t,chunkResults:s,error:l}=(0,r.useContext)(i),[n,c]=(0,r.useState)([0,10]),[d,o]=(0,r.useState)([]),m={};s.forEach(e=>{e.channel_data&&Object.entries(e.channel_data).forEach(e=>{let[t,s]=e;m[t]||(m[t]=[]),m[t].push(...s)})});let u=Object.keys(m),w=d.length>0?d:u,_=Object.values(m).reduce((e,t)=>e+t.length,0);(0,r.useEffect)(()=>{u.length>0&&0===d.length&&o(u.slice(0,Math.min(10,u.length)))},[u.length]);let S=e=>{let t="next"===e?10:-10;c(e=>{let[s,a]=e;return[Math.max(0,s+t),Math.max(10,a+t)]})},A=e=>{let t="in"===e?.5:2;c(e=>{let[s,a]=e,r=(s+a)/2,l=(a-s)/2*t;return[Math.max(0,r-l),r+l]})};return(0,a.jsxs)("div",{className:"h-screen w-screen flex flex-col bg-white",children:[(0,a.jsxs)("header",{className:"flex-shrink-0 h-16 bg-white border-b border-gray-200 px-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-lg font-bold text-black",children:"Biormika HFO Detector"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Real-time High-Frequency Oscillation Detection"})]}),(0,a.jsx)(h,{isConnected:e,progress:t,dataPoints:_,error:l})]}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsx)(g,{progress:t,label:"Analysis Progress",variant:"medical",size:"sm"})})]}),u.length>0?(0,a.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,a.jsxs)("div",{className:"flex-shrink-0 h-12 bg-gray-50 border-b border-gray-200 px-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("h2",{className:"text-sm font-semibold text-gray-900",children:"EEG Signal Analysis"}),(0,a.jsxs)("span",{className:"text-xs text-gray-600",children:["Showing ",w.length," of ",u.length," channels"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 bg-white rounded p-1",children:[(0,a.jsx)("button",{onClick:()=>S("prev"),className:"p-1 hover:bg-gray-100 rounded transition-colors",title:"Previous 10 seconds",children:(0,a.jsx)(f.A,{className:"w-3 h-3 text-gray-600"})}),(0,a.jsxs)("span",{className:"px-2 text-xs font-medium text-gray-700",children:[n[0],"s - ",n[1],"s"]}),(0,a.jsx)("button",{onClick:()=>S("next"),className:"p-1 hover:bg-gray-100 rounded transition-colors",title:"Next 10 seconds",children:(0,a.jsx)(y.A,{className:"w-3 h-3 text-gray-600"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 bg-white rounded p-1",children:[(0,a.jsx)("button",{onClick:()=>A("in"),className:"p-1 hover:bg-gray-100 rounded transition-colors",title:"Zoom in",children:(0,a.jsx)(j.A,{className:"w-3 h-3 text-gray-600"})}),(0,a.jsx)("button",{onClick:()=>A("out"),className:"p-1 hover:bg-gray-100 rounded transition-colors",title:"Zoom out",children:(0,a.jsx)(b.A,{className:"w-3 h-3 text-gray-600"})})]}),(0,a.jsx)("button",{className:"p-1 hover:bg-gray-100 rounded transition-colors",children:(0,a.jsx)(N.A,{className:"w-3 h-3 text-gray-600"})})]})]}),(0,a.jsx)(v,{channelData:m,visibleChannels:w,timeWindow:n,samplingRate:256,showHFOMarkers:!1,hfoEvents:[],channelHeight:80})]}),(0,a.jsxs)("div",{className:"flex-shrink-0 w-64 bg-gray-50 border-l border-gray-200 overflow-hidden flex flex-col",children:[(0,a.jsx)("div",{className:"p-3 border-b border-gray-200",children:(0,a.jsxs)("h3",{className:"text-xs font-semibold text-gray-900 flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"w-3 h-3 text-gray-600"}),"Channel Selection"]})}),(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto p-3",children:[(0,a.jsx)("div",{className:"space-y-1",children:u.map((e,t)=>(0,a.jsxs)("label",{className:(0,x.A)("flex items-center gap-2 p-1.5 rounded cursor-pointer transition-all","hover:bg-white",d.includes(e)&&"bg-white border border-gray-300"),children:[(0,a.jsx)("input",{type:"checkbox",checked:d.includes(e),onChange:()=>(e=>{o(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])})(e),className:"w-3 h-3 text-gray-700 border-gray-300 rounded focus:ring-gray-500"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-700",children:e}),(0,a.jsx)("div",{className:"h-1 mt-1 rounded-full",style:{background:d.includes(e)?"#000000":"#d1d5db"}})]})]},e))}),(0,a.jsxs)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:[(0,a.jsx)("button",{onClick:()=>o(u),className:"w-full text-xs text-gray-700 hover:text-black font-medium",children:"Select All"}),(0,a.jsx)("button",{onClick:()=>o([]),className:"w-full text-xs text-gray-600 hover:text-gray-700 font-medium mt-1",children:"Clear Selection"})]})]})]})]}):(0,a.jsx)("div",{className:"flex-1 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-pulse mb-4",children:(0,a.jsx)(p.A,{className:"w-16 h-16 text-gray-400 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e?"Waiting for EEG Data":"Connecting to Server"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e?"The analysis will begin shortly. Please wait...":"Establishing WebSocket connection..."})]})})]})};var _=s(4416),S=s(5339),A=s(7434);let k=e=>{let{onFileSelect:t,onStartAnalysis:s,onFileValidated:l,error:n}=e,[i,c]=(0,r.useState)(""),[d,o]=(0,r.useState)(!1),[m,h]=(0,r.useState)(!1);return(0,r.useRef)(null),(0,a.jsx)("div",{className:"card-soft",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"File path"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",value:i,onChange:e=>(e=>{c(e),t(e)})(e.target.value),placeholder:"/path/to/file.edf",className:"input-modern"}),i&&(0,a.jsx)("button",{onClick:()=>{c(""),t("")},className:"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,a.jsx)(_.A,{className:"w-4 h-4"})})]})]}),n&&(0,a.jsxs)("div",{className:"mt-4 p-3 rounded bg-red-50 border border-red-200 flex items-start gap-2",children:[(0,a.jsx)(S.A,{className:"w-4 h-4 text-red-500 flex-shrink-0 mt-0.5"}),(0,a.jsx)("p",{className:"text-sm text-red-700",children:n})]}),i&&(0,a.jsxs)("div",{className:"mt-3 p-2 rounded bg-gray-50 border border-gray-200 flex items-center gap-2",children:[(0,a.jsx)(A.A,{className:"w-4 h-4 text-gray-600"}),(0,a.jsx)("span",{className:"text-sm text-gray-700 truncate",children:i})]}),(0,a.jsx)("button",{onClick:async()=>{if(l&&i){h(!0);try{await l(i)}catch(e){}finally{h(!1)}}else s()},disabled:!i||m,className:(0,x.A)("gradient-button w-full mt-4",(!i||m)&&"opacity-50 cursor-not-allowed"),children:m?(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Validating File..."]}):l?"Load File & Configure Parameters":"Start Analysis"})]})})},E={thresholds:{amplitude1:2,amplitude2:2,peaks1:6,peaks2:3,duration:10,temporal_sync:10,spatial_sync:10},montage:{type:"bipolar"},frequency:{low_cutoff:50,high_cutoff:300},time_segment:{mode:"entire_file"},channel_selection:{selected_leads:[],contact_specifications:{}}},C={step:"file_selection",parameters:E,validationErrors:[],isValidating:!1,canStartAnalysis:!1};function T(e,t){switch(t.type){case"SET_FILE_INFO":return{...e,fileInfo:t.payload,step:"parameter_configuration"};case"UPDATE_PARAMETERS":return{...e,parameters:{...e.parameters,...t.payload}};case"SET_VALIDATION_ERRORS":return{...e,validationErrors:t.payload};case"SET_VALIDATING":return{...e,isValidating:t.payload};case"SET_CAN_START_ANALYSIS":return{...e,canStartAnalysis:t.payload};case"RESET_PARAMETERS":return{...e,parameters:E,validationErrors:[],canStartAnalysis:!1};case"SET_STEP":return{...e,step:t.payload};case"SET_PARAMETER_OPTIONS":return{...e,parameterOptions:t.payload};default:return e}}let I=(0,r.createContext)(void 0),R=()=>{let e=(0,r.useContext)(I);if(!e)throw Error("useParameters must be used within a ParameterProvider");return e},O=e=>{let{children:t}=e,[s,l]=(0,r.useReducer)(T,C);(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("http://localhost:8000/api/parameters/options");if(e.ok){let t=await e.json();l({type:"SET_PARAMETER_OPTIONS",payload:t})}}catch(e){console.error("Failed to load parameter options:",e)}})()},[]);let n=(0,r.useCallback)(e=>{l({type:"SET_FILE_INFO",payload:e})},[]),i=(0,r.useCallback)(e=>{l({type:"UPDATE_PARAMETERS",payload:e})},[]),c=(0,r.useCallback)(e=>{l({type:"UPDATE_PARAMETERS",payload:{thresholds:{...s.parameters.thresholds,...e}}})},[s.parameters.thresholds]),d=(0,r.useCallback)(e=>{l({type:"UPDATE_PARAMETERS",payload:{montage:{...s.parameters.montage,...e}}})},[s.parameters.montage]),o=(0,r.useCallback)(e=>{l({type:"UPDATE_PARAMETERS",payload:{frequency:{...s.parameters.frequency,...e}}})},[s.parameters.frequency]),m=(0,r.useCallback)(e=>{l({type:"UPDATE_PARAMETERS",payload:{time_segment:{...s.parameters.time_segment,...e}}})},[s.parameters.time_segment]),x=(0,r.useCallback)(e=>{l({type:"UPDATE_PARAMETERS",payload:{channel_selection:{...s.parameters.channel_selection,...e}}})},[s.parameters.channel_selection]),h=(0,r.useCallback)(async()=>{if(!s.fileInfo)return!1;l({type:"SET_VALIDATING",payload:!0});try{let e=await fetch("http://localhost:8000/api/analyze/start",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filepath:s.fileInfo.filepath,parameters:{thresholds:s.parameters.thresholds,montage:s.parameters.montage,frequency:s.parameters.frequency,time_segment:s.parameters.time_segment,channel_selection:s.parameters.channel_selection}})});if(e.ok)return l({type:"SET_VALIDATION_ERRORS",payload:[]}),l({type:"SET_CAN_START_ANALYSIS",payload:!0}),!0;{let t=await e.json(),s=t.errors||[{field:"general",message:t.detail||"Validation failed"}];return l({type:"SET_VALIDATION_ERRORS",payload:s}),l({type:"SET_CAN_START_ANALYSIS",payload:!1}),!1}}catch(e){return l({type:"SET_VALIDATION_ERRORS",payload:[{field:"general",message:e instanceof Error?e.message:"Validation failed"}]}),l({type:"SET_CAN_START_ANALYSIS",payload:!1}),!1}finally{l({type:"SET_VALIDATING",payload:!1})}},[s.fileInfo,s.parameters]),u=(0,r.useCallback)(()=>{l({type:"RESET_PARAMETERS"})},[]),g=(0,r.useCallback)(e=>{l({type:"UPDATE_PARAMETERS",payload:e})},[]),p=(0,r.useCallback)(e=>{l({type:"SET_STEP",payload:e})},[]);(0,r.useEffect)(()=>{if("parameter_configuration"===s.step&&s.fileInfo){let e=setTimeout(()=>{h()},500);return()=>clearTimeout(e)}},[s.parameters,s.step,s.fileInfo,h]);let f={state:s,updateFileInfo:n,updateParameters:i,updateThresholds:c,updateMontage:d,updateFrequency:o,updateTimeSegment:m,updateChannelSelection:x,validateParameters:h,resetParameters:u,loadParameters:g,setStep:p,parameterOptions:s.parameterOptions};return(0,a.jsx)(I.Provider,{value:f,children:t})};var F=s(9869),P=s(381),H=s(133),M=s(5690);let D=()=>{let{state:e,updateThresholds:t,parameterOptions:s}=R(),{thresholds:r}=e.parameters,n=(null==s?void 0:s.thresholds)||{amplitude1:{min:2,max:5,default:2,description:"HFO amp >= energy signal by (times of std)"},amplitude2:{min:2,max:5,default:2,description:"HFO amp >= mean baseline signal by (times of std)"},peaks1:{min:2,max:8,default:6,description:"Number of peaks in HFO >= Amplitude 1"},peaks2:{min:2,max:6,default:3,description:"Number of peaks in HFO >= Amplitude 2"},duration:{min:5,max:15,default:10,description:"HFO length >= (ms)"},temporal_sync:{min:5,max:12,default:10,description:"Inter HFO interval in any channel <= (ms)"},spatial_sync:{min:5,max:12,default:10,description:"Inter HFO interval across channels <= (ms)"}},i=e=>{let{parameter:s,label:l,description:i}=e,c=n[s],d=((e,t)=>{let s=[];for(let a=e;a<=t;a++)s.push(a);return s})(c.min,c.max);return(0,a.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0",children:[(0,a.jsxs)("div",{className:"flex-1 pr-4",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 text-sm",children:l}),(0,a.jsx)("div",{className:"text-xs text-gray-600 mt-1",children:i})]}),(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("select",{value:r[s],onChange:e=>{t({[s]:parseInt(e.target.value)})},className:"w-20 px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:d.map(e=>(0,a.jsx)("option",{value:e,children:e},e))})})]})};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)(l.A,{className:"w-5 h-5 text-green-600"}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"HFO Detection Thresholds"})]}),(0,a.jsxs)("div",{className:"space-y-0",children:[(0,a.jsx)(i,{parameter:"amplitude1",label:"Amplitude 1",description:n.amplitude1.description}),(0,a.jsx)(i,{parameter:"amplitude2",label:"Amplitude 2",description:n.amplitude2.description}),(0,a.jsx)(i,{parameter:"peaks1",label:"Peaks 1",description:n.peaks1.description}),(0,a.jsx)(i,{parameter:"peaks2",label:"Peaks 2",description:n.peaks2.description}),(0,a.jsx)(i,{parameter:"duration",label:"Duration",description:n.duration.description}),(0,a.jsx)(i,{parameter:"temporal_sync",label:"Temporal Synchronization",description:n.temporal_sync.description}),(0,a.jsx)(i,{parameter:"spatial_sync",label:"Spatial Synchronization",description:n.spatial_sync.description})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-blue-50 rounded-md",children:(0,a.jsxs)("p",{className:"text-xs text-blue-700",children:[(0,a.jsx)("span",{className:"font-medium",children:"Note:"})," These thresholds control the sensitivity of HFO detection. Lower values increase sensitivity but may include more noise. Higher values are more conservative."]})})]})};var L=s(9302);let z=()=>{let{state:e,updateMontage:t}=R(),{montage:s}=e.parameters,{fileInfo:r}=e,l=e=>{t({type:e,reference_channel:"referential"===e?s.reference_channel:void 0})},n=(null==r?void 0:r.channels)||[];return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)(L.A,{className:"w-5 h-5 text-purple-600"}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Select Montage for Analysis"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("label",{className:"flex items-start gap-3 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",name:"montage",value:"bipolar",checked:"bipolar"===s.type,onChange:()=>l("bipolar"),className:"mt-1 w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Bipolar"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Each channel is referenced to its adjacent channel. Provides good localization and cancels out common noise. Requires at least 2 channels."})]})]}),(0,a.jsxs)("label",{className:"flex items-start gap-3 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",name:"montage",value:"average",checked:"average"===s.type,onChange:()=>l("average"),className:"mt-1 w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Average Reference"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Each channel is referenced to the average of all channels. Useful when no neutral reference is available."})]})]}),(0,a.jsxs)("label",{className:"flex items-start gap-3 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",name:"montage",value:"referential",checked:"referential"===s.type,onChange:()=>l("referential"),className:"mt-1 w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Referential"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"All channels are referenced to a specific reference channel. Select the reference channel below."})]})]}),"referential"===s.type&&(0,a.jsxs)("div",{className:"ml-7 mt-3 p-4 bg-gray-50 rounded-md",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Reference Channel"}),(0,a.jsxs)("select",{value:s.reference_channel||"",onChange:e=>{t({reference_channel:e.target.value})},className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"Select reference channel..."}),n.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]}),!s.reference_channel&&(0,a.jsx)("p",{className:"text-xs text-red-600 mt-1",children:"Please select a reference channel for referential montage"})]})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-purple-50 rounded-md",children:(0,a.jsxs)("p",{className:"text-xs text-purple-700",children:[(0,a.jsx)("span",{className:"font-medium",children:"Tip:"})," Bipolar montage is typically preferred for HFO detection as it provides better spatial resolution and noise cancellation."]})})]})};var V=s(1539);let q=()=>{let{state:e,updateFrequency:t,parameterOptions:s}=R(),{frequency:r}=e.parameters,{fileInfo:l}=e,n=(null==s?void 0:s.frequency)||{low_cutoff_options:[1,4,8,14,30,50,70,80,120,200,250],high_cutoff_options:[4,8,14,30,50,70,80,120,160,200,300,330,600,660],default_low:50,default_high:300},i=(null==l?void 0:l.sampling_rate)||1e3,c=i/3,d=r.low_cutoff>=r.high_cutoff?"Low cutoff must be less than high cutoff":r.high_cutoff>c?"High cutoff should be ≤ ".concat(c.toFixed(0),"Hz (sampling rate / 3)"):null;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)(V.A,{className:"w-5 h-5 text-yellow-600"}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Select Frequency Band for Analysis"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Low Cutoff Filter (Hz)"}),(0,a.jsx)("select",{value:r.low_cutoff,onChange:e=>{t({low_cutoff:parseInt(e.target.value)})},className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-yellow-500 focus:border-transparent",children:n.low_cutoff_options.map(e=>(0,a.jsxs)("option",{value:e,children:[e," Hz"]},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"High Cutoff Filter (Hz)"}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mb-2",children:["High cutoff should be ≤ 1/3 of sampling rate (",c.toFixed(0),"Hz)"]}),(0,a.jsx)("select",{value:r.high_cutoff,onChange:e=>{t({high_cutoff:parseInt(e.target.value)})},className:"w-full px-3 py-2 border rounded-md bg-white focus:ring-2 focus:ring-yellow-500 focus:border-transparent ".concat(r.high_cutoff>c?"border-red-300 bg-red-50":"border-gray-300"),children:n.high_cutoff_options.map(e=>(0,a.jsxs)("option",{value:e,disabled:e>c,children:[e," Hz ",e>c?"(exceeds limit)":""]},e))})]}),(0,a.jsx)("div",{className:"p-4 bg-gray-50 rounded-md",children:(0,a.jsx)("div",{className:"text-sm text-gray-700",children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Low Cutoff"}),(0,a.jsxs)("div",{className:"text-lg font-mono",children:[r.low_cutoff," Hz"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"High Cutoff"}),(0,a.jsxs)("div",{className:"text-lg font-mono",children:[r.high_cutoff," Hz"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Bandwidth"}),(0,a.jsxs)("div",{className:"text-lg font-mono",children:[r.high_cutoff-r.low_cutoff," Hz"]})]})]})})}),d&&(0,a.jsxs)("div",{className:"flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-md",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 text-red-500 flex-shrink-0 mt-0.5"}),(0,a.jsx)("p",{className:"text-sm text-red-700",children:d})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-50 rounded-md",children:(0,a.jsxs)("p",{className:"text-xs text-blue-700",children:[(0,a.jsx)("span",{className:"font-medium",children:"File Info:"})," Sampling rate is ",i,"Hz. Maximum recommended high cutoff is ",c.toFixed(0),"Hz for optimal signal quality."]})})]})]})};var U=s(4186);let W=()=>{let{state:e,updateTimeSegment:t}=R(),{time_segment:s}=e.parameters,{fileInfo:r}=e,l=e=>{t({mode:e})},n=(e,s)=>{t({[e]:s})},i=(e,t)=>{if(!e||!t)return"";try{let[s,a,r]=e.split("."),l=2===r.length?"20".concat(r):r;return"".concat(s,"/").concat(a,"/").concat(l," ").concat(t)}catch(s){return"".concat(e," ").concat(t)}},c=r?i(r.start_date,r.start_time):"",d=r?i(r.end_date,r.end_time):"",o=(null==r?void 0:r.duration_seconds)||0,m=e=>/^\d{2}\.\d{2}\.\d{2}$/.test(e),x=e=>/^\d{2}:\d{2}:\d{2}$/.test(e);return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)(U.A,{className:"w-5 h-5 text-indigo-600"}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Specify Time Segment"})]}),r&&(0,a.jsx)("div",{className:"mb-6 p-3 bg-indigo-50 rounded-md",children:(0,a.jsxs)("div",{className:"text-sm text-indigo-700",children:[(0,a.jsx)("div",{className:"font-medium mb-1",children:"File Information:"}),(0,a.jsxs)("div",{children:["Start: ",c]}),(0,a.jsxs)("div",{children:["End: ",d]}),(0,a.jsxs)("div",{children:["Duration: ",o.toFixed(1)," seconds"]})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("label",{className:"flex items-start gap-3 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",name:"timeSegment",value:"entire_file",checked:"entire_file"===s.mode,onChange:()=>l("entire_file"),className:"mt-1 w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Entire File"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Analyze the complete EDF file from start to end."})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-start gap-3 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",name:"timeSegment",value:"start_end_times",checked:"start_end_times"===s.mode,onChange:()=>l("start_end_times"),className:"mt-1 w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Start/End Times"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Specify exact start and end date/time for analysis."})]})]}),"start_end_times"===s.mode&&(0,a.jsxs)("div",{className:"ml-7 space-y-4 p-4 bg-gray-50 rounded-md",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date (dd.mm.yy)"}),(0,a.jsx)("input",{type:"text",placeholder:"dd.mm.yy",value:s.start_date||"",onChange:e=>n("start_date",e.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ".concat(s.start_date&&!m(s.start_date)?"border-red-300 bg-red-50":"border-gray-300")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Time (HH:MM:SS)"}),(0,a.jsx)("input",{type:"text",placeholder:"HH:MM:SS",value:s.start_time||"",onChange:e=>n("start_time",e.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ".concat(s.start_time&&!x(s.start_time)?"border-red-300 bg-red-50":"border-gray-300")})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date (dd.mm.yy)"}),(0,a.jsx)("input",{type:"text",placeholder:"dd.mm.yy",value:s.end_date||"",onChange:e=>n("end_date",e.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ".concat(s.end_date&&!m(s.end_date)?"border-red-300 bg-red-50":"border-gray-300")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Time (HH:MM:SS)"}),(0,a.jsx)("input",{type:"text",placeholder:"HH:MM:SS",value:s.end_time||"",onChange:e=>n("end_time",e.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ".concat(s.end_time&&!x(s.end_time)?"border-red-300 bg-red-50":"border-gray-300")})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-start gap-3 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",name:"timeSegment",value:"start_time_duration",checked:"start_time_duration"===s.mode,onChange:()=>l("start_time_duration"),className:"mt-1 w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Start Time/Duration"}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Specify start time and duration in seconds."})]})]}),"start_time_duration"===s.mode&&(0,a.jsxs)("div",{className:"ml-7 space-y-4 p-4 bg-gray-50 rounded-md",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date (dd.mm.yy)"}),(0,a.jsx)("input",{type:"text",placeholder:"dd.mm.yy",value:s.start_date||"",onChange:e=>n("start_date",e.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ".concat(s.start_date&&!m(s.start_date)?"border-red-300 bg-red-50":"border-gray-300")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Time (HH:MM:SS)"}),(0,a.jsx)("input",{type:"text",placeholder:"HH:MM:SS",value:s.start_time||"",onChange:e=>n("start_time",e.target.value),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ".concat(s.start_time&&!x(s.start_time)?"border-red-300 bg-red-50":"border-gray-300")})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Duration (seconds)"}),(0,a.jsx)("input",{type:"number",min:"1",max:o,placeholder:"Duration in seconds",value:s.duration_seconds||"",onChange:e=>n("duration_seconds",parseFloat(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent"}),o>0&&(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Maximum duration: ",o.toFixed(1)," seconds"]})]})]})]})]})]})};var G=s(9145),B=s(8979);let J=()=>{let{state:e,updateChannelSelection:t}=R(),{channel_selection:s}=e.parameters,{fileInfo:l}=e,[n,i]=(0,r.useState)(new Set),c=(()=>{if(null==l?void 0:l.channel_groups)return l.channel_groups;let e={},t=["EKG","REF","E","C"];return(null==l?void 0:l.channels)&&(l.channels.forEach(s=>{let a=s.match(/^(?:POL |P )?(\w+?)(\d+)$/);if(a){let[,s,r]=a;t.includes(s.toUpperCase())||(e[s]||(e[s]=[]),e[s].push(parseInt(r)))}}),Object.keys(e).forEach(t=>{e[t].sort((e,t)=>e-t)})),e})(),d=Object.keys(c);(0,r.useEffect)(()=>{i(new Set([...n,...d.filter(e=>s.selected_leads.includes(e))]))},[s.selected_leads]);let o=e=>{if(!e)return[];let t=[];for(let s of e.split(",")){let e=s.trim();if(e.includes("-")){let[s,a]=e.split("-").map(e=>parseInt(e.trim()));if(!isNaN(s)&&!isNaN(a))for(let e=s;e<=a;e++)t.push(e)}else{let s=parseInt(e);isNaN(s)||t.push(s)}}return[...new Set(t)].sort((e,t)=>e-t)},m=(()=>{let e=0;return s.selected_leads.forEach(t=>{let a=o(s.contact_specifications[t]||"");e+=a.length}),e})(),x=m>=2;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 text-teal-600"}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Select Contacts to Analyze"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[m," channel",1!==m?"s":""," selected"]}),(0,a.jsx)("button",{onClick:()=>{let e={};d.forEach(t=>{let s=c[t]||[];if(s.length>0){let a=Math.min(...s),r=Math.max(...s);e[t]=s.length>1?"".concat(a,"-").concat(r):"".concat(a)}}),t({selected_leads:d,contact_specifications:e}),i(new Set(d))},className:"text-sm text-teal-600 hover:text-teal-700 font-medium",children:"Select All"}),(0,a.jsx)("button",{onClick:()=>{t({selected_leads:[],contact_specifications:{}})},className:"text-sm text-gray-600 hover:text-gray-700 font-medium",children:"Clear All"})]})]}),!x&&(0,a.jsxs)("div",{className:"mb-6 flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-md",children:[(0,a.jsx)(S.A,{className:"w-4 h-4 text-red-500 flex-shrink-0 mt-0.5"}),(0,a.jsx)("p",{className:"text-sm text-red-700",children:"At least 2 channels must be selected before starting the analysis."})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-4",children:'Enter individual contact numbers separated by comma or range separated by dash (e.g., "1-5,7,9" for contacts 1,2,3,4,5,7,9)'}),0===d.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(p.A,{className:"w-12 h-12 text-gray-300 mx-auto mb-3"}),(0,a.jsx)("p",{children:"No channel groups found in the selected file."})]}):(0,a.jsx)("div",{className:"space-y-3",children:d.map(e=>{let r=c[e]||[],l=s.selected_leads.includes(e),d=n.has(e),m=s.contact_specifications[e]||"",x=l?((e,t)=>{if(!t.trim())return"Contact specification required";let s=o(t),a=c[e]||[];if(0===s.length)return"Invalid contact specification format";let r=s.filter(e=>!a.includes(e));return r.length>0?"Invalid contacts: ".concat(r.join(", "),". Available: ").concat(a.join(", ")):null})(e,m):null,h=l?o(m):[];return(0,a.jsxs)("div",{className:"border border-gray-200 rounded-md",children:[(0,a.jsx)("div",{className:"p-4 cursor-pointer transition-colors ".concat(l?"bg-teal-50 border-teal-200":"bg-gray-50 hover:bg-gray-100"),onClick:()=>(e=>{let t=new Set(n);t.has(e)?t.delete(e):t.add(e),i(t)})(e),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("button",{onClick:a=>{a.stopPropagation(),(e=>{let a,r=s.selected_leads.includes(e),l={...s.contact_specifications};if(r)a=s.selected_leads.filter(t=>t!==e),delete l[e];else{a=[...s.selected_leads,e];let t=c[e]||[];if(t.length>0){let s=Math.min(...t),a=Math.max(...t);l[e]=t.length>1?"".concat(s,"-").concat(a):"".concat(s)}}t({selected_leads:a,contact_specifications:l})})(e)},className:"flex items-center justify-center w-5 h-5 transition-colors",children:l?(0,a.jsx)(G.A,{className:"w-5 h-5 text-teal-600"}):(0,a.jsx)(B.A,{className:"w-5 h-5 text-gray-400 hover:text-gray-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium text-gray-900",children:[e," (",r.length," contacts)"]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Available contacts: ",r.join(", ")]}),l&&h.length>0&&(0,a.jsxs)("div",{className:"text-sm text-teal-700 mt-1",children:["Selected: ",h.join(", ")," (",h.length," contacts)"]})]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-400",children:d?"▼":"▶"})]})}),d&&l&&(0,a.jsxs)("div",{className:"p-4 border-t border-gray-200 bg-white",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Contact Specification for ",e]}),(0,a.jsx)("input",{type:"text",value:m,onChange:a=>{var r;return r=a.target.value,void t({contact_specifications:{...s.contact_specifications,[e]:r}})},placeholder:"e.g., 1-".concat(r.length>1?r[r.length-1]:r[0]),className:"w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-teal-500 focus:border-transparent ".concat(x?"border-red-300 bg-red-50":"border-gray-300")}),x&&(0,a.jsx)("p",{className:"text-xs text-red-600 mt-1",children:x}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-2",children:'Examples: "1-5" (contacts 1 to 5), "1,3,5" (contacts 1, 3, and 5), "1-3,5,7-9" (mixed ranges and individual)'})]})]},e)})}),(0,a.jsx)("div",{className:"mt-6 p-3 bg-teal-50 rounded-md",children:(0,a.jsxs)("p",{className:"text-xs text-teal-700",children:[(0,a.jsx)("span",{className:"font-medium",children:"Note:"})," Channel selection determines which electrode contacts will be analyzed for HFO detection. For bipolar montage, adjacent contacts will be subtracted. Select contacts that cover the region of interest for your analysis."]})})]})};var $=s(4631);let Y=()=>{let{state:e}=R(),{validationErrors:t,isValidating:s,canStartAnalysis:r}=e;if(!s&&0===t.length&&!r)return null;let l=s?{title:"Validating Parameters...",color:"blue"}:t.length>0?{title:"Validation Failed (".concat(t.length," error").concat(t.length>1?"s":"",")"),color:"red"}:r?{title:"Parameters Valid - Ready to Analyze",color:"green"}:{title:"Parameters Not Validated",color:"yellow"};return(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border-2 p-6 ".concat("red"===l.color?"border-red-200":"green"===l.color?"border-green-200":"blue"===l.color?"border-blue-200":"border-yellow-200"),children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:s?(0,a.jsx)($.A,{className:"w-5 h-5 text-blue-500 animate-spin"}):t.length>0?(0,a.jsx)(S.A,{className:"w-5 h-5 text-red-500"}):r?(0,a.jsx)(u.A,{className:"w-5 h-5 text-green-500"}):(0,a.jsx)(o.A,{className:"w-5 h-5 text-yellow-500"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2 ".concat("red"===l.color?"text-red-900":"green"===l.color?"text-green-900":"blue"===l.color?"text-blue-900":"text-yellow-900"),children:l.title}),s&&(0,a.jsx)("div",{className:"text-sm text-blue-700",children:(0,a.jsx)("p",{children:"Checking parameter configuration against file properties and analysis requirements..."})}),t.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("p",{className:"text-sm text-red-700",children:"Please fix the following issues before starting the analysis:"}),(0,a.jsx)("div",{className:"space-y-2",children:t.map((e,t)=>(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(S.A,{className:"w-4 h-4 text-red-500 flex-shrink-0 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-red-900 text-sm",children:"general"===e.field?"General Error":"".concat(e.field.charAt(0).toUpperCase()+e.field.slice(1)," Error")}),(0,a.jsx)("div",{className:"text-sm text-red-700 mt-1",children:e.message}),e.value&&(0,a.jsxs)("div",{className:"text-xs text-red-600 mt-1 font-mono bg-red-100 px-2 py-1 rounded",children:["Current value: ",JSON.stringify(e.value)]})]})]})},t))})]}),r&&0===t.length&&(0,a.jsxs)("div",{className:"text-sm text-green-700",children:[(0,a.jsx)("p",{className:"mb-2",children:"All parameters have been validated successfully!"}),(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-3",children:(0,a.jsxs)("div",{className:"text-xs text-green-800",children:[(0,a.jsx)("div",{className:"font-medium mb-2",children:"Validation Summary:"}),(0,a.jsxs)("ul",{className:"space-y-1",children:[(0,a.jsx)("li",{children:"✓ File format and header validated"}),(0,a.jsx)("li",{children:"✓ Threshold parameters within valid ranges"}),(0,a.jsx)("li",{children:"✓ Frequency filters compatible with sampling rate"}),(0,a.jsx)("li",{children:"✓ Montage configuration valid"}),(0,a.jsx)("li",{children:"✓ Time segment within file duration"}),(0,a.jsx)("li",{children:"✓ Minimum 2 channels selected"}),(0,a.jsx)("li",{children:"✓ Channel specifications valid"})]})]})})]}),!s&&!r&&0===t.length&&(0,a.jsx)("div",{className:"text-sm text-yellow-700",children:(0,a.jsx)("p",{children:"Parameters will be validated automatically as you make changes. Make sure to configure all required settings before starting the analysis."})})]})]})})};var K=s(4229);let Z=()=>{var e;let{state:t,loadParameters:s,resetParameters:l}=R(),[n,i]=(0,r.useState)(!1),[c,d]=(0,r.useState)(null),o=()=>{if(t.fileInfo){let e=t.fileInfo.filename.replace(/\.[^/.]+$/,"");return"hfo-settings-".concat(e)}return"hfo-settings-default"},m=()=>{let e=o();return null!==localStorage.getItem(e)};return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>i(!n),className:"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(P.A,{className:"w-4 h-4"}),"Settings"]}),n&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>i(!1)}),(0,a.jsx)("div",{className:"absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-20",children:(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsxs)("div",{className:"text-xs font-medium text-gray-500 px-2 py-1 mb-2",children:["Local Settings (",(null==(e=t.fileInfo)?void 0:e.filename)||"No file",")"]}),(0,a.jsxs)("button",{onClick:()=>{try{var e;let s={parameters:t.parameters,timestamp:new Date().toISOString(),filename:(null==(e=t.fileInfo)?void 0:e.filename)||"unknown"},a=o();localStorage.setItem(a,JSON.stringify(s)),d("Settings saved successfully!"),setTimeout(()=>d(null),3e3),i(!1)}catch(e){d("Failed to save settings"),setTimeout(()=>d(null),3e3)}},className:"w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors",children:[(0,a.jsx)(K.A,{className:"w-4 h-4"}),"Save Settings"]}),(0,a.jsxs)("button",{onClick:()=>{try{let e=o(),t=localStorage.getItem(e);if(t){let e=JSON.parse(t);s(e.parameters),d("Settings loaded successfully!"),setTimeout(()=>d(null),3e3)}else d("No saved settings found for this file"),setTimeout(()=>d(null),3e3);i(!1)}catch(e){d("Failed to load settings"),setTimeout(()=>d(null),3e3)}},disabled:!m(),className:"w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md transition-colors ".concat(m()?"text-gray-700 hover:bg-gray-100":"text-gray-400 cursor-not-allowed"),children:[(0,a.jsx)(F.A,{className:"w-4 h-4"}),"Load Settings",m()&&(0,a.jsx)("span",{className:"text-xs text-green-600 ml-auto",children:"✓"})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 my-2"}),(0,a.jsx)("div",{className:"text-xs font-medium text-gray-500 px-2 py-1 mb-2",children:"Import/Export"}),(0,a.jsxs)("button",{onClick:()=>{try{var e,s;let a={parameters:t.parameters,timestamp:new Date().toISOString(),filename:(null==(e=t.fileInfo)?void 0:e.filename)||"unknown"},r=JSON.stringify(a,null,2),l=new Blob([r],{type:"application/json"}),n=URL.createObjectURL(l),c=document.createElement("a");c.href=n,c.download="hfo-settings-".concat((null==(s=t.fileInfo)?void 0:s.filename)||"export","-").concat(new Date().toISOString().split("T")[0],".json"),document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(n),d("Settings exported successfully!"),setTimeout(()=>d(null),3e3),i(!1)}catch(e){d("Failed to export settings"),setTimeout(()=>d(null),3e3)}},className:"w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors",children:[(0,a.jsx)(N.A,{className:"w-4 h-4"}),"Export Settings"]}),(0,a.jsxs)("button",{onClick:()=>{let e=document.createElement("input");e.type="file",e.accept=".json",e.onchange=e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];if(a){let e=new FileReader;e.onload=e=>{try{var t;let a=null==(t=e.target)?void 0:t.result,r=JSON.parse(a);s(r.parameters),d("Settings imported from ".concat(r.filename)),setTimeout(()=>d(null),3e3)}catch(e){d("Failed to import settings - invalid file format"),setTimeout(()=>d(null),3e3)}},e.readAsText(a)}},e.click(),i(!1)},className:"w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors",children:[(0,a.jsx)(A.A,{className:"w-4 h-4"}),"Import Settings"]}),(0,a.jsx)("div",{className:"border-t border-gray-200 my-2"}),(0,a.jsxs)("button",{onClick:()=>{l(),d("Settings reset to defaults"),setTimeout(()=>d(null),3e3),i(!1)},className:"w-full flex items-center gap-3 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors",children:[(0,a.jsx)(H.A,{className:"w-4 h-4"}),"Reset to Defaults"]})]})})]}),c&&(0,a.jsx)("div",{className:"fixed top-4 right-4 z-50 bg-white border border-gray-200 rounded-md shadow-lg px-4 py-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(c.includes("successfully")||c.includes("imported")?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{className:"text-sm text-gray-900",children:c})]})})]})},Q=e=>{let{onStartAnalysis:t}=e,{state:s,resetParameters:r,validateParameters:l,setStep:n}=R(),i=async()=>{await l()&&(n("analysis"),t())};if(!s.fileInfo)return(0,a.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(F.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No File Selected"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Please select an EDF file to configure parameters."})]})});let{fileInfo:c}=s;return(0,a.jsxs)("div",{className:"max-w-7xl mx-auto p-6 space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)(P.A,{className:"w-6 h-6 text-blue-600"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"HFO Analysis Configuration"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"File:"})," ",c.filename]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Duration:"})," ",c.duration_seconds.toFixed(1),"s"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Sampling Rate:"})," ",c.sampling_rate,"Hz"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Channels:"})," ",c.channels.length]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(Z,{}),(0,a.jsxs)("button",{onClick:r,className:"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(H.A,{className:"w-4 h-4"}),"Reset"]}),(0,a.jsx)("button",{onClick:()=>{n("file_selection")},className:"px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors",children:"Back to File Selection"})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(D,{}),(0,a.jsx)(z,{})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(q,{}),(0,a.jsx)(W,{})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(J,{}),(0,a.jsx)(Y,{})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"text-sm text-gray-600",children:s.isValidating?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),(0,a.jsx)("span",{children:"Validating parameters..."})]}):s.validationErrors.length>0?(0,a.jsxs)("span",{className:"text-red-600",children:[s.validationErrors.length," validation error",s.validationErrors.length>1?"s":""," found"]}):s.canStartAnalysis?(0,a.jsx)("span",{className:"text-green-600",children:"Parameters validated - ready to analyze"}):(0,a.jsx)("span",{children:"Configure parameters and validate before starting analysis"})}),(0,a.jsxs)("button",{onClick:i,disabled:!s.canStartAnalysis||s.isValidating,className:"flex items-center gap-2 px-6 py-3 rounded-md font-medium transition-all ".concat(s.canStartAnalysis&&!s.isValidating?"bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg":"bg-gray-200 text-gray-500 cursor-not-allowed"),children:[(0,a.jsx)(M.A,{className:"w-5 h-5"}),"Start HFO Analysis"]})]})})]})};function X(){let{state:e,updateFileInfo:t,setStep:s}=R(),[n,i]=(0,r.useState)(""),d=async e=>{if(!e)return void i("Please enter a file path");try{i("");let s=await fetch("http://localhost:8000/api/analyze",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filepath:e})});if(!s.ok){let e=await s.json();throw Error(e.detail||"Failed to load file")}let a=await s.json(),r={filename:e.split("/").pop()||e,filepath:e,start_date:a.file_info.start_date,start_time:a.file_info.start_time,end_date:o(a.file_info.start_date,a.file_info.start_time,a.file_info.duration_seconds),end_time:m(a.file_info.start_time,a.file_info.duration_seconds),sampling_rate:a.file_info.sampling_rate,max_frequency:a.file_info.sampling_rate/3,channels:a.file_info.channels,channel_groups:x(a.file_info.channels),duration_seconds:a.file_info.duration_seconds};t(r)}catch(e){i(e instanceof Error?e.message:"Failed to load file")}},o=(e,t,s)=>{try{let[a,r,l]=e.split("."),[n,i,c]=t.split(/[.:]/).map(e=>parseInt(e)),d=new Date(2e3+parseInt(l),parseInt(r)-1,parseInt(a),n,i,c);return new Date(d.getTime()+1e3*s).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit",year:"2-digit"}).replace(/\//g,".")}catch(t){return e}},m=(e,t)=>{try{let[s,a,r]=e.split(/[.:]/).map(e=>parseInt(e)),l=new Date;return l.setHours(s,a,r),new Date(l.getTime()+1e3*t).toTimeString().slice(0,8)}catch(t){return e}},x=e=>{let t={},s=["EKG","REF","E","C"];return e.forEach(e=>{let a=e.match(/^(?:POL |P )?(\w+?)(\d+)$/);if(a){let[,e,r]=a;s.includes(e.toUpperCase())||(t[e]||(t[e]=[]),t[e].push(parseInt(r)))}}),Object.keys(t).forEach(e=>{t[e].sort((e,t)=>e-t)}),t};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:["file_selection"===e.step&&(0,a.jsx)("main",{className:"mx-auto px-6 py-8 min-h-screen",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-3 mb-4",children:[(0,a.jsx)(l.A,{className:"w-8 h-8 text-blue-600"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Biormika HFO Detector"})]}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:"High-Frequency Oscillation Detection and Analysis"})]}),(0,a.jsx)(k,{onFileSelect:()=>{},onStartAnalysis:()=>{},error:n,onFileValidated:d})]})}),"parameter_configuration"===e.step&&(0,a.jsx)(Q,{onStartAnalysis:()=>{s("analysis")}}),"analysis"===e.step&&(0,a.jsx)(c,{children:(0,a.jsx)(w,{})})]})}function ee(){return(0,a.jsx)(O,{children:(0,a.jsx)(X,{})})}},5476:(e,t,s)=>{Promise.resolve().then(s.bind(s,4526))}},e=>{e.O(0,[775,441,964,358],()=>e(e.s=5476)),_N_E=e.O()}]);