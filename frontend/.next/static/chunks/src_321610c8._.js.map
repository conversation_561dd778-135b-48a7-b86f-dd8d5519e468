{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/contexts/WebSocketContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useEffect, useState, useRef } from 'react';\r\n\r\ninterface ChunkResult {\r\n  chunk_number: number;\r\n  total_chunks: number;\r\n  time_range: [number, number];\r\n  hfo_events: unknown[];\r\n  channel_data: Record<string, number[]>;\r\n  progress: number;\r\n}\r\n\r\ninterface WebSocketContextType {\r\n  isConnected: boolean;\r\n  progress: number;\r\n  chunkResults: ChunkResult[];\r\n  error: string | null;\r\n}\r\n\r\nconst WebSocketContext = createContext<WebSocketContextType>({\r\n  isConnected: false,\r\n  progress: 0,\r\n  chunkResults: [],\r\n  error: null,\r\n});\r\n\r\nexport const useWebSocket = () => useContext(WebSocketContext);\r\n\r\nexport const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\r\n  const [isConnected, setIsConnected] = useState(false);\r\n  const [progress, setProgress] = useState(0);\r\n  const [chunkResults, setChunkResults] = useState<ChunkResult[]>([]);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const wsRef = useRef<WebSocket | null>(null);\r\n\r\n  useEffect(() => {\r\n    const ws = new WebSocket('ws://localhost:8000/ws');\r\n    wsRef.current = ws;\r\n\r\n    ws.onopen = () => {\r\n      console.log('WebSocket connected');\r\n      setIsConnected(true);\r\n      setError(null);\r\n    };\r\n\r\n    ws.onmessage = (event) => {\r\n      const message = JSON.parse(event.data);\r\n      console.log('WebSocket message:', message);\r\n\r\n      switch (message.type) {\r\n        case 'status':\r\n          console.log('Status:', message.message);\r\n          break;\r\n        \r\n        case 'preview':\r\n          console.log('Preview data received:', message.data);\r\n          break;\r\n        \r\n        case 'chunk':\r\n          setChunkResults(prev => [...prev, message.data]);\r\n          setProgress(message.data.progress);\r\n          break;\r\n        \r\n        case 'complete':\r\n          console.log('Analysis complete:', message.data);\r\n          setProgress(100);\r\n          break;\r\n        \r\n        case 'error':\r\n          setError(message.message);\r\n          console.error('WebSocket error:', message);\r\n          break;\r\n      }\r\n    };\r\n\r\n    ws.onerror = (error) => {\r\n      console.error('WebSocket error:', error);\r\n      setError('Connection error');\r\n      setIsConnected(false);\r\n    };\r\n\r\n    ws.onclose = () => {\r\n      console.log('WebSocket disconnected');\r\n      setIsConnected(false);\r\n    };\r\n\r\n    return () => {\r\n      if (ws.readyState === WebSocket.OPEN) {\r\n        ws.close();\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <WebSocketContext.Provider value={{ isConnected, progress, chunkResults, error }}>\r\n      {children}\r\n    </WebSocketContext.Provider>\r\n  );\r\n};"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAoBA,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAwB;IAC3D,aAAa;IACb,UAAU;IACV,cAAc,EAAE;IAChB,OAAO;AACT;AAEO,MAAM,eAAe;;IAAM,OAAA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AAAgB;GAAhD;AAEN,MAAM,oBAA6D;QAAC,EAAE,QAAQ,EAAE;;IACrF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,KAAK,IAAI,UAAU;YACzB,MAAM,OAAO,GAAG;YAEhB,GAAG,MAAM;+CAAG;oBACV,QAAQ,GAAG,CAAC;oBACZ,eAAe;oBACf,SAAS;gBACX;;YAEA,GAAG,SAAS;+CAAG,CAAC;oBACd,MAAM,UAAU,KAAK,KAAK,CAAC,MAAM,IAAI;oBACrC,QAAQ,GAAG,CAAC,sBAAsB;oBAElC,OAAQ,QAAQ,IAAI;wBAClB,KAAK;4BACH,QAAQ,GAAG,CAAC,WAAW,QAAQ,OAAO;4BACtC;wBAEF,KAAK;4BACH,QAAQ,GAAG,CAAC,0BAA0B,QAAQ,IAAI;4BAClD;wBAEF,KAAK;4BACH;+DAAgB,CAAA,OAAQ;2CAAI;wCAAM,QAAQ,IAAI;qCAAC;;4BAC/C,YAAY,QAAQ,IAAI,CAAC,QAAQ;4BACjC;wBAEF,KAAK;4BACH,QAAQ,GAAG,CAAC,sBAAsB,QAAQ,IAAI;4BAC9C,YAAY;4BACZ;wBAEF,KAAK;4BACH,SAAS,QAAQ,OAAO;4BACxB,QAAQ,KAAK,CAAC,oBAAoB;4BAClC;oBACJ;gBACF;;YAEA,GAAG,OAAO;+CAAG,CAAC;oBACZ,QAAQ,KAAK,CAAC,oBAAoB;oBAClC,SAAS;oBACT,eAAe;gBACjB;;YAEA,GAAG,OAAO;+CAAG;oBACX,QAAQ,GAAG,CAAC;oBACZ,eAAe;gBACjB;;YAEA;+CAAO;oBACL,IAAI,GAAG,UAAU,KAAK,UAAU,IAAI,EAAE;wBACpC,GAAG,KAAK;oBACV;gBACF;;QACF;sCAAG,EAAE;IAEL,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE;YAAa;YAAU;YAAc;QAAM;kBAC5E;;;;;;AAGP;IAtEa;KAAA", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ConnectionStatus.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Wifi, WifiOff, Activity, AlertTriangle } from 'lucide-react';\r\nimport clsx from 'clsx';\r\n\r\ninterface ConnectionStatusProps {\r\n  isConnected: boolean;\r\n  progress?: number;\r\n  dataPoints?: number;\r\n  error?: string;\r\n  compact?: boolean;\r\n}\r\n\r\nconst ConnectionStatus: React.FC<ConnectionStatusProps> = ({\r\n  isConnected,\r\n  progress = 0,\r\n  dataPoints = 0,\r\n  error,\r\n  compact = false\r\n}) => {\r\n  if (compact) {\r\n    return (\r\n      <div className=\"flex items-center gap-2\">\r\n        <div className={clsx(\r\n          'status-dot',\r\n          isConnected ? 'connected' : 'disconnected'\r\n        )} />\r\n        <span className=\"text-sm font-medium text-gray-600\">\r\n          {isConnected ? 'Connected' : 'Disconnected'}\r\n        </span>\r\n        {progress > 0 && progress < 100 && (\r\n          <span className=\"text-sm text-gray-500\">\r\n            • {Math.round(progress)}%\r\n          </span>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"glass-card p-4 animate-fadeIn\">\r\n      <div className=\"flex items-start justify-between\">\r\n        <div className=\"flex items-start gap-3\">\r\n          <div className=\"p-2 rounded bg-gray-50\">\r\n            {isConnected ? (\r\n              <Wifi className=\"w-5 h-5 text-black\" />\r\n            ) : error ? (\r\n              <AlertTriangle className=\"w-5 h-5 text-red-600\" />\r\n            ) : (\r\n              <WifiOff className=\"w-5 h-5 text-gray-400\" />\r\n            )}\r\n          </div>\r\n          \r\n          <div className=\"flex-1\">\r\n            <div className=\"flex items-center gap-2 mb-1\">\r\n              <h3 className=\"text-sm font-semibold text-gray-900\">\r\n                Connection Status\r\n              </h3>\r\n              <div className={clsx(\r\n                'status-dot',\r\n                isConnected ? 'connected' : 'disconnected'\r\n              )} />\r\n            </div>\r\n            \r\n            <p className={clsx(\r\n              'text-sm',\r\n              isConnected ? 'text-gray-700' : error ? 'text-red-600' : 'text-gray-500'\r\n            )}>\r\n              {isConnected \r\n                ? 'WebSocket connected' \r\n                : error \r\n                  ? 'Connection error'\r\n                  : 'Waiting for connection...'}\r\n            </p>\r\n            \r\n            {error && (\r\n              <p className=\"text-xs text-red-500 mt-1\">\r\n                {error}\r\n              </p>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {isConnected && dataPoints > 0 && (\r\n          <div className=\"text-right\">\r\n            <div className=\"flex items-center gap-1 text-gray-700\">\r\n              <Activity className=\"w-4 h-4\" />\r\n              <span className=\"text-sm font-semibold\">\r\n                {dataPoints.toLocaleString()}\r\n              </span>\r\n            </div>\r\n            <span className=\"text-xs text-gray-500\">\r\n              data points\r\n            </span>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {isConnected && progress > 0 && progress < 100 && (\r\n        <div className=\"mt-3 pt-3 border-t border-gray-100\">\r\n          <div className=\"flex items-center justify-between mb-1\">\r\n            <span className=\"text-xs font-medium text-gray-600\">\r\n              Processing\r\n            </span>\r\n            <span className=\"text-xs font-semibold text-gray-700\">\r\n              {Math.round(progress)}%\r\n            </span>\r\n          </div>\r\n          <div className=\"progress-bar\">\r\n            <div \r\n              className=\"progress-fill\"\r\n              style={{ width: `${progress}%` }}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ConnectionStatus;"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AAJA;;;;AAcA,MAAM,mBAAoD;QAAC,EACzD,WAAW,EACX,WAAW,CAAC,EACZ,aAAa,CAAC,EACd,KAAK,EACL,UAAU,KAAK,EAChB;IACC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACjB,cACA,cAAc,cAAc;;;;;;8BAE9B,6LAAC;oBAAK,WAAU;8BACb,cAAc,cAAc;;;;;;gBAE9B,WAAW,KAAK,WAAW,qBAC1B,6LAAC;oBAAK,WAAU;;wBAAwB;wBACnC,KAAK,KAAK,CAAC;wBAAU;;;;;;;;;;;;;IAKlC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,4BACC,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;+EACd,sBACF,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;6FAEzB,6LAAC,+MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;0CAIvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;0DAGpD,6LAAC;gDAAI,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACjB,cACA,cAAc,cAAc;;;;;;;;;;;;kDAIhC,6LAAC;wCAAE,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACf,WACA,cAAc,kBAAkB,QAAQ,iBAAiB;kDAExD,cACG,wBACA,QACE,qBACA;;;;;;oCAGP,uBACC,6LAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;oBAMR,eAAe,aAAa,mBAC3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDACb,WAAW,cAAc;;;;;;;;;;;;0CAG9B,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAO7C,eAAe,WAAW,KAAK,WAAW,qBACzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAoC;;;;;;0CAGpD,6LAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC;oCAAU;;;;;;;;;;;;;kCAG1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,AAAC,GAAW,OAAT,UAAS;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAO7C;KAzGM;uCA2GS", "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ProgressIndicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Activity, CheckCircle } from 'lucide-react';\r\nimport clsx from 'clsx';\r\n\r\ninterface ProgressIndicatorProps {\r\n  progress: number;\r\n  label?: string;\r\n  showPercentage?: boolean;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  variant?: 'default' | 'gradient' | 'medical';\r\n}\r\n\r\nconst ProgressIndicator: React.FC<ProgressIndicatorProps> = ({\r\n  progress,\r\n  label = 'Processing',\r\n  showPercentage = true,\r\n  size = 'md',\r\n}) => {\r\n  const isComplete = progress >= 100;\r\n  \r\n  const sizeClasses = {\r\n    sm: 'h-1',\r\n    md: 'h-1.5',\r\n    lg: 'h-2'\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {(label || showPercentage) && (\r\n        <div className=\"flex items-center justify-between mb-2\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {isComplete ? (\r\n              <CheckCircle className=\"w-4 h-4 text-black\" />\r\n            ) : (\r\n              <Activity className=\"w-4 h-4 text-gray-600 pulse\" />\r\n            )}\r\n            <span className=\"text-sm font-medium text-gray-700\">\r\n              {label}\r\n            </span>\r\n          </div>\r\n          {showPercentage && (\r\n            <span className={clsx(\r\n              'text-sm font-semibold',\r\n              isComplete ? 'text-black' : 'text-gray-600'\r\n            )}>\r\n              {Math.round(progress)}%\r\n            </span>\r\n          )}\r\n        </div>\r\n      )}\r\n      \r\n      <div className={clsx(\r\n        'w-full bg-gray-200 rounded overflow-hidden',\r\n        sizeClasses[size]\r\n      )}>\r\n        <div\r\n          className=\"h-full bg-black rounded transition-all duration-500 ease-out\"\r\n          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}\r\n        />\r\n      </div>\r\n\r\n      {isComplete && (\r\n        <div className=\"mt-2 flex items-center gap-2 animate-slideIn\">\r\n          <CheckCircle className=\"w-3.5 h-3.5 text-black\" />\r\n          <span className=\"text-xs text-black font-medium\">\r\n            Complete\r\n          </span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProgressIndicator;"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAJA;;;;AAcA,MAAM,oBAAsD;QAAC,EAC3D,QAAQ,EACR,QAAQ,YAAY,EACpB,iBAAiB,IAAI,EACrB,OAAO,IAAI,EACZ;IACC,MAAM,aAAa,YAAY;IAE/B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,CAAC,SAAS,cAAc,mBACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,2BACC,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;yFAEvB,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CAEtB,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;oBAGJ,gCACC,6LAAC;wBAAK,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAClB,yBACA,aAAa,eAAe;;4BAE3B,KAAK,KAAK,CAAC;4BAAU;;;;;;;;;;;;;0BAM9B,6LAAC;gBAAI,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACjB,8CACA,WAAW,CAAC,KAAK;0BAEjB,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,AAAC,GAAuC,OAArC,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,YAAW;oBAAG;;;;;;;;;;;YAI9D,4BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAK,WAAU;kCAAiC;;;;;;;;;;;;;;;;;;AAO3D;KA3DM;uCA6DS", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/EEGViewer/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport dynamic from 'next/dynamic';\r\nimport { useWebSocket } from '@/contexts/WebSocketContext';\r\nimport ConnectionStatus from '@/components/ConnectionStatus';\r\nimport ProgressIndicator from '@/components/ProgressIndicator';\r\nimport { Layers, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, Download } from 'lucide-react';\r\nimport clsx from 'clsx';\r\n\r\nconst ChannelGrid = dynamic(() => import('./ChannelGrid'), { \r\n  ssr: false,\r\n  loading: () => (\r\n    <div className=\"flex items-center justify-center h-96\">\r\n      <div className=\"text-center\">\r\n        <div className=\"animate-pulse\">\r\n          <Layers className=\"w-12 h-12 text-gray-400 mx-auto mb-3\" />\r\n        </div>\r\n        <p className=\"text-sm text-gray-600\">Loading visualization...</p>\r\n      </div>\r\n    </div>\r\n  )\r\n});\r\n\r\nconst EEGViewer: React.FC = () => {\r\n  const { isConnected, progress, chunkResults, error } = useWebSocket();\r\n  const [timeWindow, setTimeWindow] = useState<[number, number]>([0, 10]);\r\n  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);\r\n\r\n  const allChannelData: Record<string, number[]> = {};\r\n  \r\n  chunkResults.forEach((chunk) => {\r\n    if (chunk.channel_data) {\r\n      Object.entries(chunk.channel_data).forEach(([channel, data]) => {\r\n        if (!allChannelData[channel]) {\r\n          allChannelData[channel] = [];\r\n        }\r\n        allChannelData[channel].push(...data);\r\n      });\r\n    }\r\n  });\r\n\r\n  const channels = Object.keys(allChannelData);\r\n  const visibleChannels = selectedChannels.length > 0 ? selectedChannels : channels;\r\n  \r\n  const totalDataPoints = Object.values(allChannelData).reduce(\r\n    (sum, data) => sum + data.length, 0\r\n  );\r\n\r\n  useEffect(() => {\r\n    if (channels.length > 0 && selectedChannels.length === 0) {\r\n      setSelectedChannels(channels.slice(0, Math.min(10, channels.length)));\r\n    }\r\n  }, [channels.length]);\r\n\r\n  const handleTimeNavigation = (direction: 'prev' | 'next') => {\r\n    const shift = direction === 'next' ? 10 : -10;\r\n    setTimeWindow(([start, end]) => [\r\n      Math.max(0, start + shift),\r\n      Math.max(10, end + shift)\r\n    ]);\r\n  };\r\n\r\n  const handleZoom = (type: 'in' | 'out') => {\r\n    const factor = type === 'in' ? 0.5 : 2;\r\n    setTimeWindow(([start, end]) => {\r\n      const center = (start + end) / 2;\r\n      const halfRange = ((end - start) / 2) * factor;\r\n      return [\r\n        Math.max(0, center - halfRange),\r\n        center + halfRange\r\n      ];\r\n    });\r\n  };\r\n\r\n  const toggleChannel = (channel: string) => {\r\n    setSelectedChannels(prev => \r\n      prev.includes(channel)\r\n        ? prev.filter(ch => ch !== channel)\r\n        : [...prev, channel]\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-screen w-screen flex flex-col bg-white\">\r\n      <header className=\"flex-shrink-0 h-16 bg-white border-b border-gray-200 px-4 flex items-center justify-between\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div>\r\n            <h1 className=\"text-lg font-bold text-black\">Biormika HFO Detector</h1>\r\n            <p className=\"text-xs text-gray-600\">Real-time High-Frequency Oscillation Detection</p>\r\n          </div>\r\n          <ConnectionStatus\r\n            isConnected={isConnected}\r\n            progress={progress}\r\n            dataPoints={totalDataPoints}\r\n            error={error}\r\n          />\r\n        </div>\r\n        <div className=\"flex items-center gap-4\">\r\n          <ProgressIndicator\r\n            progress={progress}\r\n            label=\"Analysis Progress\"\r\n            variant=\"medical\"\r\n            size=\"sm\"\r\n          />\r\n        </div>\r\n      </header>\r\n\r\n      {channels.length > 0 ? (\r\n        <div className=\"flex-1 flex overflow-hidden\">\r\n          <div className=\"flex-1 flex flex-col\">\r\n            <div className=\"flex-shrink-0 h-12 bg-gray-50 border-b border-gray-200 px-4 flex items-center justify-between\">\r\n              <div className=\"flex items-center gap-4\">\r\n                <h2 className=\"text-sm font-semibold text-gray-900\">\r\n                  EEG Signal Analysis\r\n                </h2>\r\n                <span className=\"text-xs text-gray-600\">\r\n                  Showing {visibleChannels.length} of {channels.length} channels\r\n                </span>\r\n              </div>\r\n                  \r\n              <div className=\"flex items-center gap-2\">\r\n                <div className=\"flex items-center gap-1 bg-white rounded p-1\">\r\n                  <button\r\n                    onClick={() => handleTimeNavigation('prev')}\r\n                    className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\r\n                    title=\"Previous 10 seconds\"\r\n                  >\r\n                    <ChevronLeft className=\"w-3 h-3 text-gray-600\" />\r\n                  </button>\r\n                  <span className=\"px-2 text-xs font-medium text-gray-700\">\r\n                    {timeWindow[0]}s - {timeWindow[1]}s\r\n                  </span>\r\n                  <button\r\n                    onClick={() => handleTimeNavigation('next')}\r\n                    className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\r\n                    title=\"Next 10 seconds\"\r\n                  >\r\n                    <ChevronRight className=\"w-3 h-3 text-gray-600\" />\r\n                  </button>\r\n                </div>\r\n                \r\n                <div className=\"flex items-center gap-1 bg-white rounded p-1\">\r\n                  <button\r\n                    onClick={() => handleZoom('in')}\r\n                    className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\r\n                    title=\"Zoom in\"\r\n                  >\r\n                    <ZoomIn className=\"w-3 h-3 text-gray-600\" />\r\n                  </button>\r\n                  <button\r\n                    onClick={() => handleZoom('out')}\r\n                    className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\r\n                    title=\"Zoom out\"\r\n                  >\r\n                    <ZoomOut className=\"w-3 h-3 text-gray-600\" />\r\n                  </button>\r\n                </div>\r\n                \r\n                <button className=\"p-1 hover:bg-gray-100 rounded transition-colors\">\r\n                  <Download className=\"w-3 h-3 text-gray-600\" />\r\n                </button>\r\n              </div>\r\n            </div>\r\n              \r\n            <ChannelGrid\r\n              channelData={allChannelData}\r\n              visibleChannels={visibleChannels}\r\n              timeWindow={timeWindow}\r\n              samplingRate={256}\r\n              showHFOMarkers={false}\r\n              hfoEvents={[]}\r\n              channelHeight={80}\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"flex-shrink-0 w-64 bg-gray-50 border-l border-gray-200 overflow-hidden flex flex-col\">\r\n            <div className=\"p-3 border-b border-gray-200\">\r\n              <h3 className=\"text-xs font-semibold text-gray-900 flex items-center gap-2\">\r\n                <Layers className=\"w-3 h-3 text-gray-600\" />\r\n                Channel Selection\r\n              </h3>\r\n            </div>\r\n              \r\n            <div className=\"flex-1 overflow-y-auto p-3\">\r\n              <div className=\"space-y-1\">\r\n                  {channels.map((channel, index) => (\r\n                    <label\r\n                      key={channel}\r\n                      className={clsx(\r\n                        'flex items-center gap-2 p-1.5 rounded cursor-pointer transition-all',\r\n                        'hover:bg-white',\r\n                        selectedChannels.includes(channel) && 'bg-white border border-gray-300'\r\n                      )}\r\n                    >\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={selectedChannels.includes(channel)}\r\n                        onChange={() => toggleChannel(channel)}\r\n                        className=\"w-3 h-3 text-gray-700 border-gray-300 rounded focus:ring-gray-500\"\r\n                      />\r\n                      <div className=\"flex-1\">\r\n                        <span className=\"text-xs font-medium text-gray-700\">\r\n                          {channel}\r\n                        </span>\r\n                        <div\r\n                          className=\"h-1 mt-1 rounded-full\"\r\n                          style={{\r\n                            background: selectedChannels.includes(channel) ? '#000000' : '#d1d5db'\r\n                          }}\r\n                        />\r\n                      </div>\r\n                    </label>\r\n                  ))}\r\n                </div>\r\n                \r\n              <div className=\"mt-3 pt-3 border-t border-gray-200\">\r\n                  <button\r\n                    onClick={() => setSelectedChannels(channels)}\r\n                    className=\"w-full text-xs text-gray-700 hover:text-black font-medium\"\r\n                  >\r\n                    Select All\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setSelectedChannels([])}\r\n                    className=\"w-full text-xs text-gray-600 hover:text-gray-700 font-medium mt-1\"\r\n                  >\r\n                    Clear Selection\r\n                  </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        <div className=\"flex-1 flex items-center justify-center\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-pulse mb-4\">\r\n              <Layers className=\"w-16 h-16 text-gray-400 mx-auto\" />\r\n            </div>\r\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\r\n              {isConnected ? 'Waiting for EEG Data' : 'Connecting to Server'}\r\n            </h3>\r\n            <p className=\"text-sm text-gray-600\">\r\n              {isConnected \r\n                ? 'The analysis will begin shortly. Please wait...'\r\n                : 'Establishing WebSocket connection...'}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EEGViewer;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;AARA;;;;;;;;AAUA,MAAM,cAAc,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAC1B,KAAK;IACL,SAAS,kBACP,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;KARvC;AAcN,MAAM,YAAsB;;IAC1B,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QAAC;QAAG;KAAG;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErE,MAAM,iBAA2C,CAAC;IAElD,aAAa,OAAO,CAAC,CAAC;QACpB,IAAI,MAAM,YAAY,EAAE;YACtB,OAAO,OAAO,CAAC,MAAM,YAAY,EAAE,OAAO,CAAC;oBAAC,CAAC,SAAS,KAAK;gBACzD,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;oBAC5B,cAAc,CAAC,QAAQ,GAAG,EAAE;gBAC9B;gBACA,cAAc,CAAC,QAAQ,CAAC,IAAI,IAAI;YAClC;QACF;IACF;IAEA,MAAM,WAAW,OAAO,IAAI,CAAC;IAC7B,MAAM,kBAAkB,iBAAiB,MAAM,GAAG,IAAI,mBAAmB;IAEzE,MAAM,kBAAkB,OAAO,MAAM,CAAC,gBAAgB,MAAM,CAC1D,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE;IAGpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,SAAS,MAAM,GAAG,KAAK,iBAAiB,MAAM,KAAK,GAAG;gBACxD,oBAAoB,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,SAAS,MAAM;YACpE;QACF;8BAAG;QAAC,SAAS,MAAM;KAAC;IAEpB,MAAM,uBAAuB,CAAC;QAC5B,MAAM,QAAQ,cAAc,SAAS,KAAK,CAAC;QAC3C,cAAc;gBAAC,CAAC,OAAO,IAAI;mBAAK;gBAC9B,KAAK,GAAG,CAAC,GAAG,QAAQ;gBACpB,KAAK,GAAG,CAAC,IAAI,MAAM;aACpB;;IACH;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,SAAS,SAAS,OAAO,MAAM;QACrC,cAAc;gBAAC,CAAC,OAAO,IAAI;YACzB,MAAM,SAAS,CAAC,QAAQ,GAAG,IAAI;YAC/B,MAAM,YAAY,AAAC,CAAC,MAAM,KAAK,IAAI,IAAK;YACxC,OAAO;gBACL,KAAK,GAAG,CAAC,GAAG,SAAS;gBACrB,SAAS;aACV;QACH;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,CAAA,OAClB,KAAK,QAAQ,CAAC,WACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,WACzB;mBAAI;gBAAM;aAAQ;IAE1B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6LAAC,yIAAA,CAAA,UAAgB;gCACf,aAAa;gCACb,UAAU;gCACV,YAAY;gCACZ,OAAO;;;;;;;;;;;;kCAGX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0IAAA,CAAA,UAAiB;4BAChB,UAAU;4BACV,OAAM;4BACN,SAAQ;4BACR,MAAK;;;;;;;;;;;;;;;;;YAKV,SAAS,MAAM,GAAG,kBACjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;0DAGpD,6LAAC;gDAAK,WAAU;;oDAAwB;oDAC7B,gBAAgB,MAAM;oDAAC;oDAAK,SAAS,MAAM;oDAAC;;;;;;;;;;;;;kDAIzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,qBAAqB;wDACpC,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,6LAAC;wDAAK,WAAU;;4DACb,UAAU,CAAC,EAAE;4DAAC;4DAAK,UAAU,CAAC,EAAE;4DAAC;;;;;;;kEAEpC,6LAAC;wDACC,SAAS,IAAM,qBAAqB;wDACpC,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAI5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,WAAW;wDAC1B,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,6LAAC;wDACC,SAAS,IAAM,WAAW;wDAC1B,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,+MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIvB,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAK1B,6LAAC;gCACC,aAAa;gCACb,iBAAiB;gCACjB,YAAY;gCACZ,cAAc;gCACd,gBAAgB;gCAChB,WAAW,EAAE;gCACb,eAAe;;;;;;;;;;;;kCAInB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;;;;;;0CAKhD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACV,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gDAEC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,uEACA,kBACA,iBAAiB,QAAQ,CAAC,YAAY;;kEAGxC,6LAAC;wDACC,MAAK;wDACL,SAAS,iBAAiB,QAAQ,CAAC;wDACnC,UAAU,IAAM,cAAc;wDAC9B,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EACb;;;;;;0EAEH,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,YAAY,iBAAiB,QAAQ,CAAC,WAAW,YAAY;gEAC/D;;;;;;;;;;;;;+CArBC;;;;;;;;;;kDA4Bb,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDACC,SAAS,IAAM,oBAAoB;gDACnC,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,oBAAoB,EAAE;gDACrC,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yEAQX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,6LAAC;4BAAG,WAAU;sCACX,cAAc,yBAAyB;;;;;;sCAE1C,6LAAC;4BAAE,WAAU;sCACV,cACG,oDACA;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GApOM;;QACmD,uIAAA,CAAA,eAAY;;;MAD/D;uCAsOS", "debugId": null}}, {"offset": {"line": 1112, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/FileUploadCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, DragEvent } from \"react\";\r\nimport { Upload, FileText, X, AlertCircle } from \"lucide-react\";\r\nimport clsx from \"clsx\";\r\n\r\ninterface FileUploadCardProps {\r\n  onFileSelect: (filepath: string) => void;\r\n  onStartAnalysis: () => void;\r\n  onFileValidated?: (filepath: string) => Promise<void>;\r\n  error?: string;\r\n}\r\n\r\nconst FileUploadCard: React.FC<FileUploadCardProps> = ({ onFileSelect, onStartAnalysis, onFileValidated, error }) => {\r\n  const [filepath, setFilepath] = useState(\"\");\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [isValidating, setIsValidating] = useState(false);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(true);\r\n  };\r\n\r\n  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n  };\r\n\r\n  const handleDrop = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n\r\n    const files = e.dataTransfer.files;\r\n    if (files.length > 0) {\r\n      const file = files[0];\r\n      if (file.name.endsWith(\".edf\")) {\r\n        setFilepath(file.name);\r\n        onFileSelect(file.name);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (value: string) => {\r\n    setFilepath(value);\r\n    onFileSelect(value);\r\n  };\r\n\r\n  const clearInput = () => {\r\n    setFilepath(\"\");\r\n    onFileSelect(\"\");\r\n  };\r\n\r\n  return (\r\n    <div className=\"card-soft\">\r\n      <div className=\"p-6\">\r\n        <div className=\"mt-4\">\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">File path</label>\r\n          <div className=\"relative\">\r\n            <input\r\n              type=\"text\"\r\n              value={filepath}\r\n              onChange={(e) => handleInputChange(e.target.value)}\r\n              placeholder=\"/path/to/file.edf\"\r\n              className=\"input-modern\"\r\n            />\r\n            {filepath && (\r\n              <button onClick={clearInput} className=\"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600\">\r\n                <X className=\"w-4 h-4\" />\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {error && (\r\n          <div className=\"mt-4 p-3 rounded bg-red-50 border border-red-200 flex items-start gap-2\">\r\n            <AlertCircle className=\"w-4 h-4 text-red-500 flex-shrink-0 mt-0.5\" />\r\n            <p className=\"text-sm text-red-700\">{error}</p>\r\n          </div>\r\n        )}\r\n\r\n        {filepath && (\r\n          <div className=\"mt-3 p-2 rounded bg-gray-50 border border-gray-200 flex items-center gap-2\">\r\n            <FileText className=\"w-4 h-4 text-gray-600\" />\r\n            <span className=\"text-sm text-gray-700 truncate\">{filepath}</span>\r\n          </div>\r\n        )}\r\n\r\n        <button\r\n          onClick={async () => {\r\n            if (onFileValidated && filepath) {\r\n              setIsValidating(true);\r\n              try {\r\n                await onFileValidated(filepath);\r\n              } catch (error) {\r\n                // Error will be handled by parent component\r\n              } finally {\r\n                setIsValidating(false);\r\n              }\r\n            } else {\r\n              onStartAnalysis();\r\n            }\r\n          }}\r\n          disabled={!filepath || isValidating}\r\n          className={clsx(\"gradient-button w-full mt-4\", (!filepath || isValidating) && \"opacity-50 cursor-not-allowed\")}\r\n        >\r\n          {isValidating ? (\r\n            <div className=\"flex items-center justify-center gap-2\">\r\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\r\n              Validating File...\r\n            </div>\r\n          ) : onFileValidated ? (\r\n            \"Load File & Configure Parameters\"\r\n          ) : (\r\n            \"Start Analysis\"\r\n          )}\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileUploadCard;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAaA,MAAM,iBAAgD;QAAC,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,KAAK,EAAE;;IAC9G,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;gBAC9B,YAAY,KAAK,IAAI;gBACrB,aAAa,KAAK,IAAI;YACxB;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;QACZ,aAAa;IACf;IAEA,MAAM,aAAa;QACjB,YAAY;QACZ,aAAa;IACf;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAChE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACjD,aAAY;oCACZ,WAAU;;;;;;gCAEX,0BACC,6LAAC;oCAAO,SAAS;oCAAY,WAAU;8CACrC,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAMpB,uBACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;gBAIxC,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAK,WAAU;sCAAkC;;;;;;;;;;;;8BAItD,6LAAC;oBACC,SAAS;wBACP,IAAI,mBAAmB,UAAU;4BAC/B,gBAAgB;4BAChB,IAAI;gCACF,MAAM,gBAAgB;4BACxB,EAAE,OAAO,OAAO;4BACd,4CAA4C;4BAC9C,SAAU;gCACR,gBAAgB;4BAClB;wBACF,OAAO;4BACL;wBACF;oBACF;oBACA,UAAU,CAAC,YAAY;oBACvB,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,+BAA+B,CAAC,CAAC,YAAY,YAAY,KAAK;8BAE7E,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;4BAAkE;;;;;;mEAGjF,kBACF,qCAEA;;;;;;;;;;;;;;;;;AAMZ;GA3GM;KAAA;uCA6GS", "debugId": null}}, {"offset": {"line": 1332, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/types/eeg.ts"], "sourcesContent": ["export interface ThresholdParameters {\r\n  amplitude1: number;\r\n  amplitude2: number;\r\n  peaks1: number;\r\n  peaks2: number;\r\n  duration: number;\r\n  temporal_sync: number;\r\n  spatial_sync: number;\r\n}\r\n\r\nexport interface ThresholdParameterOptions {\r\n  amplitude1: { min: number; max: number; default: number; description: string };\r\n  amplitude2: { min: number; max: number; default: number; description: string };\r\n  peaks1: { min: number; max: number; default: number; description: string };\r\n  peaks2: { min: number; max: number; default: number; description: string };\r\n  duration: { min: number; max: number; default: number; description: string };\r\n  temporal_sync: { min: number; max: number; default: number; description: string };\r\n  spatial_sync: { min: number; max: number; default: number; description: string };\r\n}\r\n\r\nexport interface MontageConfig {\r\n  type: 'bipolar' | 'average' | 'referential';\r\n  reference_channel?: string;\r\n}\r\n\r\nexport interface FrequencyFilter {\r\n  low_cutoff: number;\r\n  high_cutoff: number;\r\n}\r\n\r\nexport interface FrequencyFilterOptions {\r\n  low_cutoff_options: number[];\r\n  high_cutoff_options: number[];\r\n  default_low: number;\r\n  default_high: number;\r\n}\r\n\r\nexport interface TimeSegment {\r\n  mode: 'entire_file' | 'start_end_times' | 'start_time_duration';\r\n  start_date?: string;\r\n  start_time?: string;\r\n  end_date?: string;\r\n  end_time?: string;\r\n  duration_seconds?: number;\r\n}\r\n\r\nexport interface ChannelSelection {\r\n  selected_leads: string[];\r\n  contact_specifications: Record<string, string>;\r\n}\r\n\r\nexport interface AnalysisParameters {\r\n  thresholds: ThresholdParameters;\r\n  montage: MontageConfig;\r\n  frequency: FrequencyFilter;\r\n  time_segment: TimeSegment;\r\n  channel_selection: ChannelSelection;\r\n}\r\n\r\nexport interface FileInfo {\r\n  file_id?: string;\r\n  filename: string;\r\n  filepath: string;\r\n  start_date: string;\r\n  start_time: string;\r\n  end_date: string;\r\n  end_time: string;\r\n  sampling_rate: number;\r\n  max_frequency: number;\r\n  channels: string[];\r\n  channel_groups: Record<string, number[]>;\r\n  duration_seconds: number;\r\n}\r\n\r\nexport interface JobStatus {\r\n  job_id: string;\r\n  file_id: string;\r\n  status: 'queued' | 'processing' | 'completed' | 'error';\r\n  progress: number;\r\n  error?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface HFOEvent {\r\n  channel: string;\r\n  start_time: number;\r\n  end_time: number;\r\n  amplitude: number;\r\n  frequency: number;\r\n}\r\n\r\nexport interface ChunkResult {\r\n  type: string;\r\n  time_range: [number, number];\r\n  hfo_events: HFOEvent[];\r\n  channel_data: Record<string, number[]>;\r\n  progress: number;\r\n  chunk_number: number;\r\n  total_chunks: number;\r\n}\r\n\r\nexport interface WebSocketMessage {\r\n  type: 'preview_ready' | 'chunk_complete' | 'hfo_detected' | 'analysis_complete' | 'error' | 'status_update' | 'validation_error' | 'validation_warning';\r\n  data?: Record<string, unknown>;\r\n  message?: string;\r\n  status?: string;\r\n  progress?: number;\r\n  errors?: ValidationError[];\r\n  warnings?: string[];\r\n}\r\n\r\nexport interface ValidationError {\r\n  field: string;\r\n  message: string;\r\n  value?: unknown;\r\n}\r\n\r\nexport interface ParameterOptions {\r\n  thresholds: ThresholdParameterOptions;\r\n  montage: {\r\n    types: string[];\r\n    default: string;\r\n  };\r\n  frequency: FrequencyFilterOptions;\r\n  time_segment: {\r\n    modes: string[];\r\n    default: string;\r\n  };\r\n}\r\n\r\nexport interface AnalysisState {\r\n  step: 'file_selection' | 'parameter_configuration' | 'analysis' | 'results';\r\n  fileInfo?: FileInfo;\r\n  parameters: AnalysisParameters;\r\n  validationErrors: ValidationError[];\r\n  isValidating: boolean;\r\n  canStartAnalysis: boolean;\r\n}\r\n\r\nexport interface SettingsData {\r\n  parameters: AnalysisParameters;\r\n  timestamp: string;\r\n  filename: string;\r\n}\r\n\r\nexport const DEFAULT_PARAMETERS: AnalysisParameters = {\r\n  thresholds: {\r\n    amplitude1: 2,\r\n    amplitude2: 2,\r\n    peaks1: 6,\r\n    peaks2: 3,\r\n    duration: 10,\r\n    temporal_sync: 10,\r\n    spatial_sync: 10\r\n  },\r\n  montage: {\r\n    type: 'bipolar'\r\n  },\r\n  frequency: {\r\n    low_cutoff: 50,\r\n    high_cutoff: 300\r\n  },\r\n  time_segment: {\r\n    mode: 'entire_file'\r\n  },\r\n  channel_selection: {\r\n    selected_leads: [],\r\n    contact_specifications: {}\r\n  }\r\n};"], "names": [], "mappings": ";;;AAkJO,MAAM,qBAAyC;IACpD,YAAY;QACV,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,eAAe;QACf,cAAc;IAChB;IACA,SAAS;QACP,MAAM;IACR;IACA,WAAW;QACT,YAAY;QACZ,aAAa;IACf;IACA,cAAc;QACZ,MAAM;IACR;IACA,mBAAmB;QACjB,gBAAgB,EAAE;QAClB,wBAAwB,CAAC;IAC3B;AACF", "debugId": null}}, {"offset": {"line": 1369, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/contexts/ParameterContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';\r\nimport { \r\n  AnalysisState, \r\n  AnalysisParameters, \r\n  FileInfo, \r\n  ValidationError, \r\n  ParameterOptions,\r\n  DEFAULT_PARAMETERS \r\n} from '@/types/eeg';\r\n\r\ninterface ParameterContextType {\r\n  state: AnalysisState;\r\n  updateFileInfo: (fileInfo: FileInfo) => void;\r\n  updateParameters: (parameters: Partial<AnalysisParameters>) => void;\r\n  updateThresholds: (thresholds: Partial<AnalysisParameters['thresholds']>) => void;\r\n  updateMontage: (montage: Partial<AnalysisParameters['montage']>) => void;\r\n  updateFrequency: (frequency: Partial<AnalysisParameters['frequency']>) => void;\r\n  updateTimeSegment: (timeSegment: Partial<AnalysisParameters['time_segment']>) => void;\r\n  updateChannelSelection: (channelSelection: Partial<AnalysisParameters['channel_selection']>) => void;\r\n  validateParameters: () => Promise<boolean>;\r\n  resetParameters: () => void;\r\n  loadParameters: (parameters: AnalysisParameters) => void;\r\n  setStep: (step: AnalysisState['step']) => void;\r\n  parameterOptions?: ParameterOptions;\r\n}\r\n\r\ntype ParameterAction = \r\n  | { type: 'SET_FILE_INFO'; payload: FileInfo }\r\n  | { type: 'UPDATE_PARAMETERS'; payload: Partial<AnalysisParameters> }\r\n  | { type: 'SET_VALIDATION_ERRORS'; payload: ValidationError[] }\r\n  | { type: 'SET_VALIDATING'; payload: boolean }\r\n  | { type: 'SET_CAN_START_ANALYSIS'; payload: boolean }\r\n  | { type: 'RESET_PARAMETERS' }\r\n  | { type: 'SET_STEP'; payload: AnalysisState['step'] }\r\n  | { type: 'SET_PARAMETER_OPTIONS'; payload: ParameterOptions };\r\n\r\nconst initialState: AnalysisState = {\r\n  step: 'file_selection',\r\n  parameters: DEFAULT_PARAMETERS,\r\n  validationErrors: [],\r\n  isValidating: false,\r\n  canStartAnalysis: false,\r\n};\r\n\r\nfunction parameterReducer(state: AnalysisState, action: ParameterAction): AnalysisState {\r\n  switch (action.type) {\r\n    case 'SET_FILE_INFO':\r\n      return {\r\n        ...state,\r\n        fileInfo: action.payload,\r\n        step: 'parameter_configuration',\r\n      };\r\n    \r\n    case 'UPDATE_PARAMETERS':\r\n      return {\r\n        ...state,\r\n        parameters: {\r\n          ...state.parameters,\r\n          ...action.payload,\r\n        },\r\n      };\r\n    \r\n    case 'SET_VALIDATION_ERRORS':\r\n      return {\r\n        ...state,\r\n        validationErrors: action.payload,\r\n      };\r\n    \r\n    case 'SET_VALIDATING':\r\n      return {\r\n        ...state,\r\n        isValidating: action.payload,\r\n      };\r\n    \r\n    case 'SET_CAN_START_ANALYSIS':\r\n      return {\r\n        ...state,\r\n        canStartAnalysis: action.payload,\r\n      };\r\n    \r\n    case 'RESET_PARAMETERS':\r\n      return {\r\n        ...state,\r\n        parameters: DEFAULT_PARAMETERS,\r\n        validationErrors: [],\r\n        canStartAnalysis: false,\r\n      };\r\n    \r\n    case 'SET_STEP':\r\n      return {\r\n        ...state,\r\n        step: action.payload,\r\n      };\r\n    \r\n    case 'SET_PARAMETER_OPTIONS':\r\n      return {\r\n        ...state,\r\n        parameterOptions: action.payload,\r\n      };\r\n    \r\n    default:\r\n      return state;\r\n  }\r\n}\r\n\r\nconst ParameterContext = createContext<ParameterContextType | undefined>(undefined);\r\n\r\nexport const useParameters = () => {\r\n  const context = useContext(ParameterContext);\r\n  if (!context) {\r\n    throw new Error('useParameters must be used within a ParameterProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface ParameterProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport const ParameterProvider: React.FC<ParameterProviderProps> = ({ children }) => {\r\n  const [state, dispatch] = useReducer(parameterReducer, initialState);\r\n\r\n  // Load parameter options on mount\r\n  useEffect(() => {\r\n    const loadParameterOptions = async () => {\r\n      try {\r\n        const response = await fetch('http://localhost:8000/api/parameters/options');\r\n        if (response.ok) {\r\n          const options: ParameterOptions = await response.json();\r\n          dispatch({ type: 'SET_PARAMETER_OPTIONS', payload: options });\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to load parameter options:', error);\r\n      }\r\n    };\r\n\r\n    loadParameterOptions();\r\n  }, []);\r\n\r\n  const updateFileInfo = useCallback((fileInfo: FileInfo) => {\r\n    dispatch({ type: 'SET_FILE_INFO', payload: fileInfo });\r\n  }, []);\r\n\r\n  const updateParameters = useCallback((parameters: Partial<AnalysisParameters>) => {\r\n    dispatch({ type: 'UPDATE_PARAMETERS', payload: parameters });\r\n  }, []);\r\n\r\n  const updateThresholds = useCallback((thresholds: Partial<AnalysisParameters['thresholds']>) => {\r\n    dispatch({ \r\n      type: 'UPDATE_PARAMETERS', \r\n      payload: { \r\n        thresholds: { ...state.parameters.thresholds, ...thresholds } \r\n      } \r\n    });\r\n  }, [state.parameters.thresholds]);\r\n\r\n  const updateMontage = useCallback((montage: Partial<AnalysisParameters['montage']>) => {\r\n    dispatch({ \r\n      type: 'UPDATE_PARAMETERS', \r\n      payload: { \r\n        montage: { ...state.parameters.montage, ...montage } \r\n      } \r\n    });\r\n  }, [state.parameters.montage]);\r\n\r\n  const updateFrequency = useCallback((frequency: Partial<AnalysisParameters['frequency']>) => {\r\n    dispatch({ \r\n      type: 'UPDATE_PARAMETERS', \r\n      payload: { \r\n        frequency: { ...state.parameters.frequency, ...frequency } \r\n      } \r\n    });\r\n  }, [state.parameters.frequency]);\r\n\r\n  const updateTimeSegment = useCallback((timeSegment: Partial<AnalysisParameters['time_segment']>) => {\r\n    dispatch({ \r\n      type: 'UPDATE_PARAMETERS', \r\n      payload: { \r\n        time_segment: { ...state.parameters.time_segment, ...timeSegment } \r\n      } \r\n    });\r\n  }, [state.parameters.time_segment]);\r\n\r\n  const updateChannelSelection = useCallback((channelSelection: Partial<AnalysisParameters['channel_selection']>) => {\r\n    dispatch({ \r\n      type: 'UPDATE_PARAMETERS', \r\n      payload: { \r\n        channel_selection: { ...state.parameters.channel_selection, ...channelSelection } \r\n      } \r\n    });\r\n  }, [state.parameters.channel_selection]);\r\n\r\n  const validateParameters = useCallback(async (): Promise<boolean> => {\r\n    if (!state.fileInfo) {\r\n      return false;\r\n    }\r\n\r\n    dispatch({ type: 'SET_VALIDATING', payload: true });\r\n\r\n    try {\r\n      const response = await fetch('http://localhost:8000/api/analyze/start', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          filepath: state.fileInfo.filepath,\r\n          parameters: {\r\n            thresholds: state.parameters.thresholds,\r\n            montage: state.parameters.montage,\r\n            frequency: state.parameters.frequency,\r\n            time_segment: state.parameters.time_segment,\r\n            channel_selection: state.parameters.channel_selection,\r\n          },\r\n        }),\r\n      });\r\n\r\n      if (response.ok) {\r\n        dispatch({ type: 'SET_VALIDATION_ERRORS', payload: [] });\r\n        dispatch({ type: 'SET_CAN_START_ANALYSIS', payload: true });\r\n        return true;\r\n      } else {\r\n        const errorData = await response.json();\r\n        const errors: ValidationError[] = errorData.errors || [\r\n          { field: 'general', message: errorData.detail || 'Validation failed' }\r\n        ];\r\n        dispatch({ type: 'SET_VALIDATION_ERRORS', payload: errors });\r\n        dispatch({ type: 'SET_CAN_START_ANALYSIS', payload: false });\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      const validationError: ValidationError = {\r\n        field: 'general',\r\n        message: error instanceof Error ? error.message : 'Validation failed'\r\n      };\r\n      dispatch({ type: 'SET_VALIDATION_ERRORS', payload: [validationError] });\r\n      dispatch({ type: 'SET_CAN_START_ANALYSIS', payload: false });\r\n      return false;\r\n    } finally {\r\n      dispatch({ type: 'SET_VALIDATING', payload: false });\r\n    }\r\n  }, [state.fileInfo, state.parameters]);\r\n\r\n  const resetParameters = useCallback(() => {\r\n    dispatch({ type: 'RESET_PARAMETERS' });\r\n  }, []);\r\n\r\n  const loadParameters = useCallback((parameters: AnalysisParameters) => {\r\n    dispatch({ type: 'UPDATE_PARAMETERS', payload: parameters });\r\n  }, []);\r\n\r\n  const setStep = useCallback((step: AnalysisState['step']) => {\r\n    dispatch({ type: 'SET_STEP', payload: step });\r\n  }, []);\r\n\r\n  // Auto-validate when parameters change (debounced)\r\n  useEffect(() => {\r\n    if (state.step === 'parameter_configuration' && state.fileInfo) {\r\n      const timeoutId = setTimeout(() => {\r\n        validateParameters();\r\n      }, 500); // 500ms debounce\r\n\r\n      return () => clearTimeout(timeoutId);\r\n    }\r\n  }, [state.parameters, state.step, state.fileInfo, validateParameters]);\r\n\r\n  const contextValue: ParameterContextType = {\r\n    state,\r\n    updateFileInfo,\r\n    updateParameters,\r\n    updateThresholds,\r\n    updateMontage,\r\n    updateFrequency,\r\n    updateTimeSegment,\r\n    updateChannelSelection,\r\n    validateParameters,\r\n    resetParameters,\r\n    loadParameters,\r\n    setStep,\r\n    parameterOptions: state.parameterOptions,\r\n  };\r\n\r\n  return (\r\n    <ParameterContext.Provider value={contextValue}>\r\n      {children}\r\n    </ParameterContext.Provider>\r\n  );\r\n};"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAsCA,MAAM,eAA8B;IAClC,MAAM;IACN,YAAY,sHAAA,CAAA,qBAAkB;IAC9B,kBAAkB,EAAE;IACpB,cAAc;IACd,kBAAkB;AACpB;AAEA,SAAS,iBAAiB,KAAoB,EAAE,MAAuB;IACrE,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,UAAU,OAAO,OAAO;gBACxB,MAAM;YACR;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,YAAY;oBACV,GAAG,MAAM,UAAU;oBACnB,GAAG,OAAO,OAAO;gBACnB;YACF;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,kBAAkB,OAAO,OAAO;YAClC;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,cAAc,OAAO,OAAO;YAC9B;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,kBAAkB,OAAO,OAAO;YAClC;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,YAAY,sHAAA,CAAA,qBAAkB;gBAC9B,kBAAkB,EAAE;gBACpB,kBAAkB;YACpB;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,OAAO,OAAO;YACtB;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,kBAAkB,OAAO,OAAO;YAClC;QAEF;YACE,OAAO;IACX;AACF;AAEA,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAoC;AAElE,MAAM,gBAAgB;;IAC3B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,MAAM,oBAAsD;QAAC,EAAE,QAAQ,EAAE;;IAC9E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB;IAEvD,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;oEAAuB;oBAC3B,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,UAA4B,MAAM,SAAS,IAAI;4BACrD,SAAS;gCAAE,MAAM;gCAAyB,SAAS;4BAAQ;wBAC7D;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,qCAAqC;oBACrD;gBACF;;YAEA;QACF;sCAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YAClC,SAAS;gBAAE,MAAM;gBAAiB,SAAS;YAAS;QACtD;wDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACpC,SAAS;gBAAE,MAAM;gBAAqB,SAAS;YAAW;QAC5D;0DAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACpC,SAAS;gBACP,MAAM;gBACN,SAAS;oBACP,YAAY;wBAAE,GAAG,MAAM,UAAU,CAAC,UAAU;wBAAE,GAAG,UAAU;oBAAC;gBAC9D;YACF;QACF;0DAAG;QAAC,MAAM,UAAU,CAAC,UAAU;KAAC;IAEhC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACjC,SAAS;gBACP,MAAM;gBACN,SAAS;oBACP,SAAS;wBAAE,GAAG,MAAM,UAAU,CAAC,OAAO;wBAAE,GAAG,OAAO;oBAAC;gBACrD;YACF;QACF;uDAAG;QAAC,MAAM,UAAU,CAAC,OAAO;KAAC;IAE7B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACnC,SAAS;gBACP,MAAM;gBACN,SAAS;oBACP,WAAW;wBAAE,GAAG,MAAM,UAAU,CAAC,SAAS;wBAAE,GAAG,SAAS;oBAAC;gBAC3D;YACF;QACF;yDAAG;QAAC,MAAM,UAAU,CAAC,SAAS;KAAC;IAE/B,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YACrC,SAAS;gBACP,MAAM;gBACN,SAAS;oBACP,cAAc;wBAAE,GAAG,MAAM,UAAU,CAAC,YAAY;wBAAE,GAAG,WAAW;oBAAC;gBACnE;YACF;QACF;2DAAG;QAAC,MAAM,UAAU,CAAC,YAAY;KAAC;IAElC,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,CAAC;YAC1C,SAAS;gBACP,MAAM;gBACN,SAAS;oBACP,mBAAmB;wBAAE,GAAG,MAAM,UAAU,CAAC,iBAAiB;wBAAE,GAAG,gBAAgB;oBAAC;gBAClF;YACF;QACF;gEAAG;QAAC,MAAM,UAAU,CAAC,iBAAiB;KAAC;IAEvC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACrC,IAAI,CAAC,MAAM,QAAQ,EAAE;gBACnB,OAAO;YACT;YAEA,SAAS;gBAAE,MAAM;gBAAkB,SAAS;YAAK;YAEjD,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,2CAA2C;oBACtE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,UAAU,MAAM,QAAQ,CAAC,QAAQ;wBACjC,YAAY;4BACV,YAAY,MAAM,UAAU,CAAC,UAAU;4BACvC,SAAS,MAAM,UAAU,CAAC,OAAO;4BACjC,WAAW,MAAM,UAAU,CAAC,SAAS;4BACrC,cAAc,MAAM,UAAU,CAAC,YAAY;4BAC3C,mBAAmB,MAAM,UAAU,CAAC,iBAAiB;wBACvD;oBACF;gBACF;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,SAAS;wBAAE,MAAM;wBAAyB,SAAS,EAAE;oBAAC;oBACtD,SAAS;wBAAE,MAAM;wBAA0B,SAAS;oBAAK;oBACzD,OAAO;gBACT,OAAO;oBACL,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,SAA4B,UAAU,MAAM,IAAI;wBACpD;4BAAE,OAAO;4BAAW,SAAS,UAAU,MAAM,IAAI;wBAAoB;qBACtE;oBACD,SAAS;wBAAE,MAAM;wBAAyB,SAAS;oBAAO;oBAC1D,SAAS;wBAAE,MAAM;wBAA0B,SAAS;oBAAM;oBAC1D,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,kBAAmC;oBACvC,OAAO;oBACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACpD;gBACA,SAAS;oBAAE,MAAM;oBAAyB,SAAS;wBAAC;qBAAgB;gBAAC;gBACrE,SAAS;oBAAE,MAAM;oBAA0B,SAAS;gBAAM;gBAC1D,OAAO;YACT,SAAU;gBACR,SAAS;oBAAE,MAAM;oBAAkB,SAAS;gBAAM;YACpD;QACF;4DAAG;QAAC,MAAM,QAAQ;QAAE,MAAM,UAAU;KAAC;IAErC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YAClC,SAAS;gBAAE,MAAM;YAAmB;QACtC;yDAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YAClC,SAAS;gBAAE,MAAM;gBAAqB,SAAS;YAAW;QAC5D;wDAAG,EAAE;IAEL,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAC3B,SAAS;gBAAE,MAAM;gBAAY,SAAS;YAAK;QAC7C;iDAAG,EAAE;IAEL,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,MAAM,IAAI,KAAK,6BAA6B,MAAM,QAAQ,EAAE;gBAC9D,MAAM,YAAY;6DAAW;wBAC3B;oBACF;4DAAG,MAAM,iBAAiB;gBAE1B;mDAAO,IAAM,aAAa;;YAC5B;QACF;sCAAG;QAAC,MAAM,UAAU;QAAE,MAAM,IAAI;QAAE,MAAM,QAAQ;QAAE;KAAmB;IAErE,MAAM,eAAqC;QACzC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,kBAAkB,MAAM,gBAAgB;IAC1C;IAEA,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;IAxKa;KAAA", "debugId": null}}, {"offset": {"line": 1728, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/HFOThresholdPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Target } from 'lucide-react';\r\n\r\nconst HFOThresholdPanel: React.FC = () => {\r\n  const { state, updateThresholds, parameterOptions } = useParameters();\r\n  const { thresholds } = state.parameters;\r\n\r\n  // Default threshold options matching the original backend\r\n  const defaultThresholdOptions = {\r\n    amplitude1: { min: 2, max: 5, default: 2, description: \"HFO amp >= energy signal by (times of std)\" },\r\n    amplitude2: { min: 2, max: 5, default: 2, description: \"HFO amp >= mean baseline signal by (times of std)\" },\r\n    peaks1: { min: 2, max: 8, default: 6, description: \"Number of peaks in HFO >= Amplitude 1\" },\r\n    peaks2: { min: 2, max: 6, default: 3, description: \"Number of peaks in HFO >= Amplitude 2\" },\r\n    duration: { min: 5, max: 15, default: 10, description: \"HFO length >= (ms)\" },\r\n    temporal_sync: { min: 5, max: 12, default: 10, description: \"Inter HFO interval in any channel <= (ms)\" },\r\n    spatial_sync: { min: 5, max: 12, default: 10, description: \"Inter HFO interval across channels <= (ms)\" }\r\n  };\r\n\r\n  const thresholdOptions = parameterOptions?.thresholds || defaultThresholdOptions;\r\n\r\n  const handleThresholdChange = (parameter: keyof typeof thresholds, value: number) => {\r\n    updateThresholds({ [parameter]: value });\r\n  };\r\n\r\n  const createDropdownOptions = (min: number, max: number) => {\r\n    const options = [];\r\n    for (let i = min; i <= max; i++) {\r\n      options.push(i);\r\n    }\r\n    return options;\r\n  };\r\n\r\n  const ThresholdRow = ({ \r\n    parameter, \r\n    label, \r\n    description \r\n  }: { \r\n    parameter: keyof typeof thresholds;\r\n    label: string;\r\n    description: string;\r\n  }) => {\r\n    const config = thresholdOptions[parameter];\r\n    const options = createDropdownOptions(config.min, config.max);\r\n\r\n    return (\r\n      <div className=\"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg hover:from-indigo-50 hover:to-purple-50 transition-all duration-200\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex-1 pr-4\">\r\n            <div className=\"flex items-center justify-between mb-2\">\r\n              <span className=\"font-medium text-gray-900 text-sm\">{label}</span>\r\n              <span className=\"text-xs text-gray-500 bg-white px-2 py-1 rounded-full font-mono\">\r\n                {thresholds[parameter]}\r\n              </span>\r\n            </div>\r\n            <p className=\"text-xs text-gray-600 leading-relaxed\">{description}</p>\r\n          </div>\r\n        </div>\r\n        <div className=\"mt-3\">\r\n          <select\r\n            value={thresholds[parameter]}\r\n            onChange={(e) => handleThresholdChange(parameter, parseInt(e.target.value))}\r\n            className=\"select-elegant w-full\"\r\n          >\r\n            {options.map(option => (\r\n              <option key={option} value={option}>\r\n                {option}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"card-elegant p-6 slide-up\">\r\n      <div className=\"flex items-center gap-3 mb-6\">\r\n        <div className=\"p-2 rounded-lg bg-gradient-to-r from-red-100 to-pink-100\">\r\n          <Target className=\"w-5 h-5 text-red-600\" />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-lg font-semibold text-gray-900\">HFO Detection Thresholds</h2>\r\n          <p className=\"text-sm text-gray-600\">Configure sensitivity parameters for oscillation detection</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-3\">\r\n        <ThresholdRow\r\n          parameter=\"amplitude1\"\r\n          label=\"Amplitude 1\"\r\n          description={thresholdOptions.amplitude1.description}\r\n        />\r\n        \r\n        <ThresholdRow\r\n          parameter=\"amplitude2\"\r\n          label=\"Amplitude 2\"\r\n          description={thresholdOptions.amplitude2.description}\r\n        />\r\n        \r\n        <ThresholdRow\r\n          parameter=\"peaks1\"\r\n          label=\"Peaks 1\"\r\n          description={thresholdOptions.peaks1.description}\r\n        />\r\n        \r\n        <ThresholdRow\r\n          parameter=\"peaks2\"\r\n          label=\"Peaks 2\"\r\n          description={thresholdOptions.peaks2.description}\r\n        />\r\n        \r\n        <ThresholdRow\r\n          parameter=\"duration\"\r\n          label=\"Duration\"\r\n          description={thresholdOptions.duration.description}\r\n        />\r\n        \r\n        <ThresholdRow\r\n          parameter=\"temporal_sync\"\r\n          label=\"Temporal Synchronization\"\r\n          description={thresholdOptions.temporal_sync.description}\r\n        />\r\n        \r\n        <ThresholdRow\r\n          parameter=\"spatial_sync\"\r\n          label=\"Spatial Synchronization\"\r\n          description={thresholdOptions.spatial_sync.description}\r\n        />\r\n      </div>\r\n\r\n      <div className=\"mt-6 p-4 bg-gradient-to-r from-red-50 to-pink-50 rounded-lg border border-red-100\">\r\n        <p className=\"text-sm text-red-800 leading-relaxed\">\r\n          <span className=\"font-semibold\">Sensitivity Guide:</span> Higher values create more conservative detection (fewer HFOs), \r\n          while lower values increase sensitivity (more HFOs detected). Adjust based on signal quality and analysis requirements.\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HFOThresholdPanel;"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAMA,MAAM,oBAA8B;;IAClC,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD;IAClE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,UAAU;IAEvC,0DAA0D;IAC1D,MAAM,0BAA0B;QAC9B,YAAY;YAAE,KAAK;YAAG,KAAK;YAAG,SAAS;YAAG,aAAa;QAA6C;QACpG,YAAY;YAAE,KAAK;YAAG,KAAK;YAAG,SAAS;YAAG,aAAa;QAAoD;QAC3G,QAAQ;YAAE,KAAK;YAAG,KAAK;YAAG,SAAS;YAAG,aAAa;QAAwC;QAC3F,QAAQ;YAAE,KAAK;YAAG,KAAK;YAAG,SAAS;YAAG,aAAa;QAAwC;QAC3F,UAAU;YAAE,KAAK;YAAG,KAAK;YAAI,SAAS;YAAI,aAAa;QAAqB;QAC5E,eAAe;YAAE,KAAK;YAAG,KAAK;YAAI,SAAS;YAAI,aAAa;QAA4C;QACxG,cAAc;YAAE,KAAK;YAAG,KAAK;YAAI,SAAS;YAAI,aAAa;QAA6C;IAC1G;IAEA,MAAM,mBAAmB,CAAA,6BAAA,uCAAA,iBAAkB,UAAU,KAAI;IAEzD,MAAM,wBAAwB,CAAC,WAAoC;QACjE,iBAAiB;YAAE,CAAC,UAAU,EAAE;QAAM;IACxC;IAEA,MAAM,wBAAwB,CAAC,KAAa;QAC1C,MAAM,UAAU,EAAE;QAClB,IAAK,IAAI,IAAI,KAAK,KAAK,KAAK,IAAK;YAC/B,QAAQ,IAAI,CAAC;QACf;QACA,OAAO;IACT;IAEA,MAAM,eAAe;YAAC,EACpB,SAAS,EACT,KAAK,EACL,WAAW,EAKZ;QACC,MAAM,SAAS,gBAAgB,CAAC,UAAU;QAC1C,MAAM,UAAU,sBAAsB,OAAO,GAAG,EAAE,OAAO,GAAG;QAE5D,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAqC;;;;;;kDACrD,6LAAC;wCAAK,WAAU;kDACb,UAAU,CAAC,UAAU;;;;;;;;;;;;0CAG1B,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;;;;;;;;;;;;8BAG1D,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,OAAO,UAAU,CAAC,UAAU;wBAC5B,UAAU,CAAC,IAAM,sBAAsB,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;wBACzE,WAAU;kCAET,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC;gCAAoB,OAAO;0CACzB;+BADU;;;;;;;;;;;;;;;;;;;;;IAQzB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAIzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,UAAU,CAAC,WAAW;;;;;;kCAGtD,6LAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,UAAU,CAAC,WAAW;;;;;;kCAGtD,6LAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,MAAM,CAAC,WAAW;;;;;;kCAGlD,6LAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,MAAM,CAAC,WAAW;;;;;;kCAGlD,6LAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,QAAQ,CAAC,WAAW;;;;;;kCAGpD,6LAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,aAAa,CAAC,WAAW;;;;;;kCAGzD,6LAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,YAAY,CAAC,WAAW;;;;;;;;;;;;0BAI1D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;sCACX,6LAAC;4BAAK,WAAU;sCAAgB;;;;;;wBAAyB;;;;;;;;;;;;;;;;;;AAMnE;GAvIM;;QACkD,uIAAA,CAAA,gBAAa;;;KAD/D;uCAyIS", "debugId": null}}, {"offset": {"line": 2060, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/MontageSelectionPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Network } from 'lucide-react';\r\n\r\nconst MontageSelectionPanel: React.FC = () => {\r\n  const { state, updateMontage } = useParameters();\r\n  const { montage } = state.parameters;\r\n  const { fileInfo } = state;\r\n\r\n  const handleMontageTypeChange = (type: 'bipolar' | 'average' | 'referential') => {\r\n    updateMontage({ type, reference_channel: type === 'referential' ? montage.reference_channel : undefined });\r\n  };\r\n\r\n  const handleReferenceChannelChange = (referenceChannel: string) => {\r\n    updateMontage({ reference_channel: referenceChannel });\r\n  };\r\n\r\n  const availableChannels = fileInfo?.channels || [];\r\n\r\n  return (\r\n    <div className=\"card-elegant p-6 slide-up\">\r\n      <div className=\"flex items-center gap-3 mb-6\">\r\n        <div className=\"p-2 rounded-lg bg-gradient-to-r from-purple-100 to-pink-100\">\r\n          <Network className=\"w-5 h-5 text-purple-600\" />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-lg font-semibold text-gray-900\">Montage Configuration</h2>\r\n          <p className=\"text-sm text-gray-600\">Choose signal referencing method</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-4\">\r\n        {/* Bipolar Option */}\r\n        <label className=\"flex items-start gap-3 cursor-pointer\">\r\n          <input\r\n            type=\"radio\"\r\n            name=\"montage\"\r\n            value=\"bipolar\"\r\n            checked={montage.type === 'bipolar'}\r\n            onChange={() => handleMontageTypeChange('bipolar')}\r\n            className=\"mt-1 w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500\"\r\n          />\r\n          <div className=\"flex-1\">\r\n            <div className=\"font-medium text-gray-900\">Bipolar</div>\r\n            <div className=\"text-sm text-gray-600 mt-1\">\r\n              Each channel is referenced to its adjacent channel. Provides good localization and \r\n              cancels out common noise. Requires at least 2 channels.\r\n            </div>\r\n          </div>\r\n        </label>\r\n\r\n        {/* Average Option */}\r\n        <label className=\"flex items-start gap-3 cursor-pointer\">\r\n          <input\r\n            type=\"radio\"\r\n            name=\"montage\"\r\n            value=\"average\"\r\n            checked={montage.type === 'average'}\r\n            onChange={() => handleMontageTypeChange('average')}\r\n            className=\"mt-1 w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500\"\r\n          />\r\n          <div className=\"flex-1\">\r\n            <div className=\"font-medium text-gray-900\">Average Reference</div>\r\n            <div className=\"text-sm text-gray-600 mt-1\">\r\n              Each channel is referenced to the average of all channels. Useful when no neutral \r\n              reference is available.\r\n            </div>\r\n          </div>\r\n        </label>\r\n\r\n        {/* Referential Option */}\r\n        <label className=\"flex items-start gap-3 cursor-pointer\">\r\n          <input\r\n            type=\"radio\"\r\n            name=\"montage\"\r\n            value=\"referential\"\r\n            checked={montage.type === 'referential'}\r\n            onChange={() => handleMontageTypeChange('referential')}\r\n            className=\"mt-1 w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500\"\r\n          />\r\n          <div className=\"flex-1\">\r\n            <div className=\"font-medium text-gray-900\">Referential</div>\r\n            <div className=\"text-sm text-gray-600 mt-1\">\r\n              All channels are referenced to a specific reference channel. Select the reference channel below.\r\n            </div>\r\n          </div>\r\n        </label>\r\n\r\n        {/* Reference Channel Selection */}\r\n        {montage.type === 'referential' && (\r\n          <div className=\"ml-7 mt-3 p-4 bg-gray-50 rounded-md\">\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n              Reference Channel\r\n            </label>\r\n            <select\r\n              value={montage.reference_channel || ''}\r\n              onChange={(e) => handleReferenceChannelChange(e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\r\n            >\r\n              <option value=\"\">Select reference channel...</option>\r\n              {availableChannels.map(channel => (\r\n                <option key={channel} value={channel}>\r\n                  {channel}\r\n                </option>\r\n              ))}\r\n            </select>\r\n            {!montage.reference_channel && (\r\n              <p className=\"text-xs text-red-600 mt-1\">\r\n                Please select a reference channel for referential montage\r\n              </p>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"mt-4 p-3 bg-purple-50 rounded-md\">\r\n        <p className=\"text-xs text-purple-700\">\r\n          <span className=\"font-medium\">Tip:</span> Bipolar montage is typically preferred for HFO detection \r\n          as it provides better spatial resolution and noise cancellation.\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MontageSelectionPanel;"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAMA,MAAM,wBAAkC;;IACtC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD;IAC7C,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,UAAU;IACpC,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,MAAM,0BAA0B,CAAC;QAC/B,cAAc;YAAE;YAAM,mBAAmB,SAAS,gBAAgB,QAAQ,iBAAiB,GAAG;QAAU;IAC1G;IAEA,MAAM,+BAA+B,CAAC;QACpC,cAAc;YAAE,mBAAmB;QAAiB;IACtD;IAEA,MAAM,oBAAoB,CAAA,qBAAA,+BAAA,SAAU,QAAQ,KAAI,EAAE;IAElD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAIzC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAM;gCACN,SAAS,QAAQ,IAAI,KAAK;gCAC1B,UAAU,IAAM,wBAAwB;gCACxC,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA4B;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAQhD,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAM;gCACN,SAAS,QAAQ,IAAI,KAAK;gCAC1B,UAAU,IAAM,wBAAwB;gCACxC,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA4B;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAQhD,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAM;gCACN,SAAS,QAAQ,IAAI,KAAK;gCAC1B,UAAU,IAAM,wBAAwB;gCACxC,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA4B;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;oBAO/C,QAAQ,IAAI,KAAK,+BAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,OAAO,QAAQ,iBAAiB,IAAI;gCACpC,UAAU,CAAC,IAAM,6BAA6B,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,kBAAkB,GAAG,CAAC,CAAA,wBACrB,6LAAC;4CAAqB,OAAO;sDAC1B;2CADU;;;;;;;;;;;4BAKhB,CAAC,QAAQ,iBAAiB,kBACzB,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;0BAQjD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;sCACX,6LAAC;4BAAK,WAAU;sCAAc;;;;;;wBAAW;;;;;;;;;;;;;;;;;;AAMnD;GAvHM;;QAC6B,uIAAA,CAAA,gBAAa;;;KAD1C;uCAyHS", "debugId": null}}, {"offset": {"line": 2387, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/FrequencyFilterPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Zap, AlertTriangle } from 'lucide-react';\r\n\r\nconst FrequencyFilterPanel: React.FC = () => {\r\n  const { state, updateFrequency, parameterOptions } = useParameters();\r\n  const { frequency } = state.parameters;\r\n  const { fileInfo } = state;\r\n\r\n  // Default frequency options from original backend\r\n  const defaultFrequencyOptions = {\r\n    low_cutoff_options: [1, 4, 8, 14, 30, 50, 70, 80, 120, 200, 250],\r\n    high_cutoff_options: [4, 8, 14, 30, 50, 70, 80, 120, 160, 200, 300, 330, 600, 660],\r\n    default_low: 50,\r\n    default_high: 300\r\n  };\r\n\r\n  const frequencyOptions = parameterOptions?.frequency || defaultFrequencyOptions;\r\n  const samplingRate = fileInfo?.sampling_rate || 1000;\r\n  const maxRecommendedFreq = samplingRate / 3;\r\n\r\n  const handleLowCutoffChange = (lowCutoff: number) => {\r\n    updateFrequency({ low_cutoff: lowCutoff });\r\n  };\r\n\r\n  const handleHighCutoffChange = (highCutoff: number) => {\r\n    updateFrequency({ high_cutoff: highCutoff });\r\n  };\r\n\r\n  const isValidConfiguration = () => {\r\n    return frequency.low_cutoff < frequency.high_cutoff && frequency.high_cutoff <= maxRecommendedFreq;\r\n  };\r\n\r\n  const getValidationMessage = () => {\r\n    if (frequency.low_cutoff >= frequency.high_cutoff) {\r\n      return \"Low cutoff must be less than high cutoff\";\r\n    }\r\n    if (frequency.high_cutoff > maxRecommendedFreq) {\r\n      return `High cutoff should be ≤ ${maxRecommendedFreq.toFixed(0)}Hz (sampling rate / 3)`;\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const validationMessage = getValidationMessage();\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n      <div className=\"flex items-center gap-3 mb-6\">\r\n        <Zap className=\"w-5 h-5 text-yellow-600\" />\r\n        <h2 className=\"text-lg font-semibold text-gray-900\">Select Frequency Band for Analysis</h2>\r\n      </div>\r\n\r\n      <div className=\"space-y-6\">\r\n        {/* Low Cutoff Filter */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Low Cutoff Filter (Hz)\r\n          </label>\r\n          <select\r\n            value={frequency.low_cutoff}\r\n            onChange={(e) => handleLowCutoffChange(parseInt(e.target.value))}\r\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-yellow-500 focus:border-transparent\"\r\n          >\r\n            {frequencyOptions.low_cutoff_options.map(option => (\r\n              <option key={option} value={option}>\r\n                {option} Hz\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* High Cutoff Filter */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            High Cutoff Filter (Hz)\r\n          </label>\r\n          <div className=\"text-xs text-gray-500 mb-2\">\r\n            High cutoff should be ≤ 1/3 of sampling rate ({maxRecommendedFreq.toFixed(0)}Hz)\r\n          </div>\r\n          <select\r\n            value={frequency.high_cutoff}\r\n            onChange={(e) => handleHighCutoffChange(parseInt(e.target.value))}\r\n            className={`w-full px-3 py-2 border rounded-md bg-white focus:ring-2 focus:ring-yellow-500 focus:border-transparent ${\r\n              frequency.high_cutoff > maxRecommendedFreq \r\n                ? 'border-red-300 bg-red-50' \r\n                : 'border-gray-300'\r\n            }`}\r\n          >\r\n            {frequencyOptions.high_cutoff_options.map(option => (\r\n              <option \r\n                key={option} \r\n                value={option}\r\n                disabled={option > maxRecommendedFreq}\r\n              >\r\n                {option} Hz {option > maxRecommendedFreq ? '(exceeds limit)' : ''}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* Current Configuration Display */}\r\n        <div className=\"p-4 bg-gray-50 rounded-md\">\r\n          <div className=\"text-sm text-gray-700\">\r\n            <div className=\"grid grid-cols-3 gap-4\">\r\n              <div>\r\n                <div className=\"font-medium\">Low Cutoff</div>\r\n                <div className=\"text-lg font-mono\">{frequency.low_cutoff} Hz</div>\r\n              </div>\r\n              <div>\r\n                <div className=\"font-medium\">High Cutoff</div>\r\n                <div className=\"text-lg font-mono\">{frequency.high_cutoff} Hz</div>\r\n              </div>\r\n              <div>\r\n                <div className=\"font-medium\">Bandwidth</div>\r\n                <div className=\"text-lg font-mono\">{frequency.high_cutoff - frequency.low_cutoff} Hz</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Validation Message */}\r\n        {validationMessage && (\r\n          <div className=\"flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-md\">\r\n            <AlertTriangle className=\"w-4 h-4 text-red-500 flex-shrink-0 mt-0.5\" />\r\n            <p className=\"text-sm text-red-700\">{validationMessage}</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Sampling Rate Info */}\r\n        <div className=\"p-3 bg-blue-50 rounded-md\">\r\n          <p className=\"text-xs text-blue-700\">\r\n            <span className=\"font-medium\">File Info:</span> Sampling rate is {samplingRate}Hz. \r\n            Maximum recommended high cutoff is {maxRecommendedFreq.toFixed(0)}Hz for optimal signal quality.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FrequencyFilterPanel;"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;;;AAJA;;;AAMA,MAAM,uBAAiC;;IACrC,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD;IACjE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,UAAU;IACtC,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,kDAAkD;IAClD,MAAM,0BAA0B;QAC9B,oBAAoB;YAAC;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI;QAChE,qBAAqB;YAAC;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QAClF,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAA,6BAAA,uCAAA,iBAAkB,SAAS,KAAI;IACxD,MAAM,eAAe,CAAA,qBAAA,+BAAA,SAAU,aAAa,KAAI;IAChD,MAAM,qBAAqB,eAAe;IAE1C,MAAM,wBAAwB,CAAC;QAC7B,gBAAgB;YAAE,YAAY;QAAU;IAC1C;IAEA,MAAM,yBAAyB,CAAC;QAC9B,gBAAgB;YAAE,aAAa;QAAW;IAC5C;IAEA,MAAM,uBAAuB;QAC3B,OAAO,UAAU,UAAU,GAAG,UAAU,WAAW,IAAI,UAAU,WAAW,IAAI;IAClF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,UAAU,UAAU,IAAI,UAAU,WAAW,EAAE;YACjD,OAAO;QACT;QACA,IAAI,UAAU,WAAW,GAAG,oBAAoB;YAC9C,OAAO,AAAC,2BAAwD,OAA9B,mBAAmB,OAAO,CAAC,IAAG;QAClE;QACA,OAAO;IACT;IAEA,MAAM,oBAAoB;IAE1B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;kCACf,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;;0BAGtD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACC,OAAO,UAAU,UAAU;gCAC3B,UAAU,CAAC,IAAM,sBAAsB,SAAS,EAAE,MAAM,CAAC,KAAK;gCAC9D,WAAU;0CAET,iBAAiB,kBAAkB,CAAC,GAAG,CAAC,CAAA,uBACvC,6LAAC;wCAAoB,OAAO;;4CACzB;4CAAO;;uCADG;;;;;;;;;;;;;;;;kCAQnB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;oCAA6B;oCACK,mBAAmB,OAAO,CAAC;oCAAG;;;;;;;0CAE/E,6LAAC;gCACC,OAAO,UAAU,WAAW;gCAC5B,UAAU,CAAC,IAAM,uBAAuB,SAAS,EAAE,MAAM,CAAC,KAAK;gCAC/D,WAAW,AAAC,2GAIX,OAHC,UAAU,WAAW,GAAG,qBACpB,6BACA;0CAGL,iBAAiB,mBAAmB,CAAC,GAAG,CAAC,CAAA,uBACxC,6LAAC;wCAEC,OAAO;wCACP,UAAU,SAAS;;4CAElB;4CAAO;4CAAK,SAAS,qBAAqB,oBAAoB;;uCAJ1D;;;;;;;;;;;;;;;;kCAWb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,6LAAC;gDAAI,WAAU;;oDAAqB,UAAU,UAAU;oDAAC;;;;;;;;;;;;;kDAE3D,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,6LAAC;gDAAI,WAAU;;oDAAqB,UAAU,WAAW;oDAAC;;;;;;;;;;;;;kDAE5D,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,6LAAC;gDAAI,WAAU;;oDAAqB,UAAU,WAAW,GAAG,UAAU,UAAU;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOxF,mCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKzC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;8CACX,6LAAC;oCAAK,WAAU;8CAAc;;;;;;gCAAiB;gCAAmB;gCAAa;gCAC3C,mBAAmB,OAAO,CAAC;gCAAG;;;;;;;;;;;;;;;;;;;;;;;;AAM9E;GAtIM;;QACiD,uIAAA,CAAA,gBAAa;;;KAD9D;uCAwIS", "debugId": null}}, {"offset": {"line": 2768, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/TimeSegmentPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Clock, Calendar } from 'lucide-react';\r\n\r\nconst TimeSegmentPanel: React.FC = () => {\r\n  const { state, updateTimeSegment } = useParameters();\r\n  const { time_segment } = state.parameters;\r\n  const { fileInfo } = state;\r\n\r\n  const handleModeChange = (mode: 'entire_file' | 'start_end_times' | 'start_time_duration') => {\r\n    updateTimeSegment({ mode });\r\n  };\r\n\r\n  const handleFieldChange = (field: string, value: string) => {\r\n    updateTimeSegment({ [field]: value });\r\n  };\r\n\r\n  const formatDateTime = (dateStr: string, timeStr: string) => {\r\n    if (!dateStr || !timeStr) return '';\r\n    try {\r\n      // Convert dd.mm.yy HH:MM:SS format to readable format\r\n      const [day, month, year] = dateStr.split('.');\r\n      const fullYear = year.length === 2 ? `20${year}` : year;\r\n      return `${day}/${month}/${fullYear} ${timeStr}`;\r\n    } catch {\r\n      return `${dateStr} ${timeStr}`;\r\n    }\r\n  };\r\n\r\n  const fileStart = fileInfo ? formatDateTime(fileInfo.start_date, fileInfo.start_time) : '';\r\n  const fileEnd = fileInfo ? formatDateTime(fileInfo.end_date, fileInfo.end_time) : '';\r\n  const fileDuration = fileInfo?.duration_seconds || 0;\r\n\r\n  const validateDateFormat = (date: string) => {\r\n    return /^\\d{2}\\.\\d{2}\\.\\d{2}$/.test(date);\r\n  };\r\n\r\n  const validateTimeFormat = (time: string) => {\r\n    return /^\\d{2}:\\d{2}:\\d{2}$/.test(time);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n      <div className=\"flex items-center gap-3 mb-6\">\r\n        <Clock className=\"w-5 h-5 text-indigo-600\" />\r\n        <h2 className=\"text-lg font-semibold text-gray-900\">Specify Time Segment</h2>\r\n      </div>\r\n\r\n      {/* File Duration Info */}\r\n      {fileInfo && (\r\n        <div className=\"mb-6 p-3 bg-indigo-50 rounded-md\">\r\n          <div className=\"text-sm text-indigo-700\">\r\n            <div className=\"font-medium mb-1\">File Information:</div>\r\n            <div>Start: {fileStart}</div>\r\n            <div>End: {fileEnd}</div>\r\n            <div>Duration: {fileDuration.toFixed(1)} seconds</div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"space-y-6\">\r\n        {/* Entire File Option */}\r\n        <label className=\"flex items-start gap-3 cursor-pointer\">\r\n          <input\r\n            type=\"radio\"\r\n            name=\"timeSegment\"\r\n            value=\"entire_file\"\r\n            checked={time_segment.mode === 'entire_file'}\r\n            onChange={() => handleModeChange('entire_file')}\r\n            className=\"mt-1 w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500\"\r\n          />\r\n          <div className=\"flex-1\">\r\n            <div className=\"font-medium text-gray-900\">Entire File</div>\r\n            <div className=\"text-sm text-gray-600 mt-1\">\r\n              Analyze the complete EDF file from start to end.\r\n            </div>\r\n          </div>\r\n        </label>\r\n\r\n        {/* Start/End Times Option */}\r\n        <div className=\"space-y-3\">\r\n          <label className=\"flex items-start gap-3 cursor-pointer\">\r\n            <input\r\n              type=\"radio\"\r\n              name=\"timeSegment\"\r\n              value=\"start_end_times\"\r\n              checked={time_segment.mode === 'start_end_times'}\r\n              onChange={() => handleModeChange('start_end_times')}\r\n              className=\"mt-1 w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500\"\r\n            />\r\n            <div className=\"flex-1\">\r\n              <div className=\"font-medium text-gray-900\">Start/End Times</div>\r\n              <div className=\"text-sm text-gray-600 mt-1\">\r\n                Specify exact start and end date/time for analysis.\r\n              </div>\r\n            </div>\r\n          </label>\r\n\r\n          {time_segment.mode === 'start_end_times' && (\r\n            <div className=\"ml-7 space-y-4 p-4 bg-gray-50 rounded-md\">\r\n              {/* Start Date/Time */}\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Start Date (dd.mm.yy)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"dd.mm.yy\"\r\n                    value={time_segment.start_date || ''}\r\n                    onChange={(e) => handleFieldChange('start_date', e.target.value)}\r\n                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${\r\n                      time_segment.start_date && !validateDateFormat(time_segment.start_date) \r\n                        ? 'border-red-300 bg-red-50' \r\n                        : 'border-gray-300'\r\n                    }`}\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Start Time (HH:MM:SS)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"HH:MM:SS\"\r\n                    value={time_segment.start_time || ''}\r\n                    onChange={(e) => handleFieldChange('start_time', e.target.value)}\r\n                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${\r\n                      time_segment.start_time && !validateTimeFormat(time_segment.start_time) \r\n                        ? 'border-red-300 bg-red-50' \r\n                        : 'border-gray-300'\r\n                    }`}\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              {/* End Date/Time */}\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    End Date (dd.mm.yy)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"dd.mm.yy\"\r\n                    value={time_segment.end_date || ''}\r\n                    onChange={(e) => handleFieldChange('end_date', e.target.value)}\r\n                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${\r\n                      time_segment.end_date && !validateDateFormat(time_segment.end_date) \r\n                        ? 'border-red-300 bg-red-50' \r\n                        : 'border-gray-300'\r\n                    }`}\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    End Time (HH:MM:SS)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"HH:MM:SS\"\r\n                    value={time_segment.end_time || ''}\r\n                    onChange={(e) => handleFieldChange('end_time', e.target.value)}\r\n                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${\r\n                      time_segment.end_time && !validateTimeFormat(time_segment.end_time) \r\n                        ? 'border-red-300 bg-red-50' \r\n                        : 'border-gray-300'\r\n                    }`}\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Start Time/Duration Option */}\r\n        <div className=\"space-y-3\">\r\n          <label className=\"flex items-start gap-3 cursor-pointer\">\r\n            <input\r\n              type=\"radio\"\r\n              name=\"timeSegment\"\r\n              value=\"start_time_duration\"\r\n              checked={time_segment.mode === 'start_time_duration'}\r\n              onChange={() => handleModeChange('start_time_duration')}\r\n              className=\"mt-1 w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500\"\r\n            />\r\n            <div className=\"flex-1\">\r\n              <div className=\"font-medium text-gray-900\">Start Time/Duration</div>\r\n              <div className=\"text-sm text-gray-600 mt-1\">\r\n                Specify start time and duration in seconds.\r\n              </div>\r\n            </div>\r\n          </label>\r\n\r\n          {time_segment.mode === 'start_time_duration' && (\r\n            <div className=\"ml-7 space-y-4 p-4 bg-gray-50 rounded-md\">\r\n              {/* Start Date/Time */}\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Start Date (dd.mm.yy)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"dd.mm.yy\"\r\n                    value={time_segment.start_date || ''}\r\n                    onChange={(e) => handleFieldChange('start_date', e.target.value)}\r\n                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${\r\n                      time_segment.start_date && !validateDateFormat(time_segment.start_date) \r\n                        ? 'border-red-300 bg-red-50' \r\n                        : 'border-gray-300'\r\n                    }`}\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Start Time (HH:MM:SS)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"HH:MM:SS\"\r\n                    value={time_segment.start_time || ''}\r\n                    onChange={(e) => handleFieldChange('start_time', e.target.value)}\r\n                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${\r\n                      time_segment.start_time && !validateTimeFormat(time_segment.start_time) \r\n                        ? 'border-red-300 bg-red-50' \r\n                        : 'border-gray-300'\r\n                    }`}\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              {/* Duration */}\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                  Duration (seconds)\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  min=\"1\"\r\n                  max={fileDuration}\r\n                  placeholder=\"Duration in seconds\"\r\n                  value={time_segment.duration_seconds || ''}\r\n                  onChange={(e) => handleFieldChange('duration_seconds', parseFloat(e.target.value))}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\r\n                />\r\n                {fileDuration > 0 && (\r\n                  <p className=\"text-xs text-gray-500 mt-1\">\r\n                    Maximum duration: {fileDuration.toFixed(1)} seconds\r\n                  </p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TimeSegmentPanel;"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAMA,MAAM,mBAA6B;;IACjC,MAAM,EAAE,KAAK,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,UAAU;IACzC,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;YAAE;QAAK;IAC3B;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,kBAAkB;YAAE,CAAC,MAAM,EAAE;QAAM;IACrC;IAEA,MAAM,iBAAiB,CAAC,SAAiB;QACvC,IAAI,CAAC,WAAW,CAAC,SAAS,OAAO;QACjC,IAAI;YACF,sDAAsD;YACtD,MAAM,CAAC,KAAK,OAAO,KAAK,GAAG,QAAQ,KAAK,CAAC;YACzC,MAAM,WAAW,KAAK,MAAM,KAAK,IAAI,AAAC,KAAS,OAAL,QAAS;YACnD,OAAO,AAAC,GAAS,OAAP,KAAI,KAAY,OAAT,OAAM,KAAe,OAAZ,UAAS,KAAW,OAAR;QACxC,EAAE,UAAM;YACN,OAAO,AAAC,GAAa,OAAX,SAAQ,KAAW,OAAR;QACvB;IACF;IAEA,MAAM,YAAY,WAAW,eAAe,SAAS,UAAU,EAAE,SAAS,UAAU,IAAI;IACxF,MAAM,UAAU,WAAW,eAAe,SAAS,QAAQ,EAAE,SAAS,QAAQ,IAAI;IAClF,MAAM,eAAe,CAAA,qBAAA,+BAAA,SAAU,gBAAgB,KAAI;IAEnD,MAAM,qBAAqB,CAAC;QAC1B,OAAO,wBAAwB,IAAI,CAAC;IACtC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,sBAAsB,IAAI,CAAC;IACpC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;;YAIrD,0BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAmB;;;;;;sCAClC,6LAAC;;gCAAI;gCAAQ;;;;;;;sCACb,6LAAC;;gCAAI;gCAAM;;;;;;;sCACX,6LAAC;;gCAAI;gCAAW,aAAa,OAAO,CAAC;gCAAG;;;;;;;;;;;;;;;;;;0BAK9C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAM;gCACN,SAAS,aAAa,IAAI,KAAK;gCAC/B,UAAU,IAAM,iBAAiB;gCACjC,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA4B;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAOhD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,OAAM;wCACN,SAAS,aAAa,IAAI,KAAK;wCAC/B,UAAU,IAAM,iBAAiB;wCACjC,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA4B;;;;;;0DAC3C,6LAAC;gDAAI,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;4BAM/C,aAAa,IAAI,KAAK,mCACrB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,aAAa,UAAU,IAAI;wDAClC,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC/D,WAAW,AAAC,kGAIX,OAHC,aAAa,UAAU,IAAI,CAAC,mBAAmB,aAAa,UAAU,IAClE,6BACA;;;;;;;;;;;;0DAIV,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,aAAa,UAAU,IAAI;wDAClC,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC/D,WAAW,AAAC,kGAIX,OAHC,aAAa,UAAU,IAAI,CAAC,mBAAmB,aAAa,UAAU,IAClE,6BACA;;;;;;;;;;;;;;;;;;kDAOZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,aAAa,QAAQ,IAAI;wDAChC,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC7D,WAAW,AAAC,kGAIX,OAHC,aAAa,QAAQ,IAAI,CAAC,mBAAmB,aAAa,QAAQ,IAC9D,6BACA;;;;;;;;;;;;0DAIV,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,aAAa,QAAQ,IAAI;wDAChC,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC7D,WAAW,AAAC,kGAIX,OAHC,aAAa,QAAQ,IAAI,CAAC,mBAAmB,aAAa,QAAQ,IAC9D,6BACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,OAAM;wCACN,SAAS,aAAa,IAAI,KAAK;wCAC/B,UAAU,IAAM,iBAAiB;wCACjC,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA4B;;;;;;0DAC3C,6LAAC;gDAAI,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;4BAM/C,aAAa,IAAI,KAAK,uCACrB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,aAAa,UAAU,IAAI;wDAClC,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC/D,WAAW,AAAC,kGAIX,OAHC,aAAa,UAAU,IAAI,CAAC,mBAAmB,aAAa,UAAU,IAClE,6BACA;;;;;;;;;;;;0DAIV,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,aAAa,UAAU,IAAI;wDAClC,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC/D,WAAW,AAAC,kGAIX,OAHC,aAAa,UAAU,IAAI,CAAC,mBAAmB,aAAa,UAAU,IAClE,6BACA;;;;;;;;;;;;;;;;;;kDAOZ,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAK;gDACL,aAAY;gDACZ,OAAO,aAAa,gBAAgB,IAAI;gDACxC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAChF,WAAU;;;;;;4CAEX,eAAe,mBACd,6LAAC;gDAAE,WAAU;;oDAA6B;oDACrB,aAAa,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/D;GA9PM;;QACiC,uIAAA,CAAA,gBAAa;;;KAD9C;uCAgQS", "debugId": null}}, {"offset": {"line": 3334, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/ChannelSelectionPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Layers, CheckSquare, Square, AlertCircle } from 'lucide-react';\r\n\r\nconst ChannelSelectionPanel: React.FC = () => {\r\n  const { state, updateChannelSelection } = useParameters();\r\n  const { channel_selection } = state.parameters;\r\n  const { fileInfo } = state;\r\n\r\n  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());\r\n\r\n  // Extract channel groups from fileInfo or parse from channel list\r\n  const extractChannelGroups = () => {\r\n    if (fileInfo?.channel_groups) {\r\n      return fileInfo.channel_groups;\r\n    }\r\n\r\n    // Fallback: parse channel names to create groups\r\n    const groups: Record<string, number[]> = {};\r\n    const excludeList = ['EKG', 'REF', 'E', 'C']; // From original code\r\n\r\n    if (fileInfo?.channels) {\r\n      fileInfo.channels.forEach(channel => {\r\n        // Clean channel name and extract group\r\n        const match = channel.match(/^(?:POL |P )?(\\w+?)(\\d+)$/);\r\n        if (match) {\r\n          const [, groupName, contactNum] = match;\r\n          if (!excludeList.includes(groupName.toUpperCase())) {\r\n            if (!groups[groupName]) {\r\n              groups[groupName] = [];\r\n            }\r\n            groups[groupName].push(parseInt(contactNum));\r\n          }\r\n        }\r\n      });\r\n\r\n      // Sort contact numbers within each group\r\n      Object.keys(groups).forEach(group => {\r\n        groups[group].sort((a, b) => a - b);\r\n      });\r\n    }\r\n\r\n    return groups;\r\n  };\r\n\r\n  const channelGroups = extractChannelGroups();\r\n  const groupNames = Object.keys(channelGroups);\r\n\r\n  // Auto-expand groups that have contacts\r\n  useEffect(() => {\r\n    const groupsWithContacts = groupNames.filter(group => \r\n      channel_selection.selected_leads.includes(group)\r\n    );\r\n    setExpandedGroups(new Set([...expandedGroups, ...groupsWithContacts]));\r\n  }, [channel_selection.selected_leads]);\r\n\r\n  const handleLeadToggle = (leadName: string) => {\r\n    const isSelected = channel_selection.selected_leads.includes(leadName);\r\n    let newSelectedLeads;\r\n    let newContactSpecs = { ...channel_selection.contact_specifications };\r\n\r\n    if (isSelected) {\r\n      // Remove lead\r\n      newSelectedLeads = channel_selection.selected_leads.filter(lead => lead !== leadName);\r\n      delete newContactSpecs[leadName];\r\n    } else {\r\n      // Add lead\r\n      newSelectedLeads = [...channel_selection.selected_leads, leadName];\r\n      // Set default contact specification (all contacts in the group)\r\n      const contacts = channelGroups[leadName] || [];\r\n      if (contacts.length > 0) {\r\n        const min = Math.min(...contacts);\r\n        const max = Math.max(...contacts);\r\n        newContactSpecs[leadName] = contacts.length > 1 ? `${min}-${max}` : `${min}`;\r\n      }\r\n    }\r\n\r\n    updateChannelSelection({\r\n      selected_leads: newSelectedLeads,\r\n      contact_specifications: newContactSpecs\r\n    });\r\n  };\r\n\r\n  const handleContactSpecChange = (leadName: string, specification: string) => {\r\n    updateChannelSelection({\r\n      contact_specifications: {\r\n        ...channel_selection.contact_specifications,\r\n        [leadName]: specification\r\n      }\r\n    });\r\n  };\r\n\r\n  const handleSelectAll = () => {\r\n    const allLeads = groupNames;\r\n    const newContactSpecs: Record<string, string> = {};\r\n\r\n    // Create default contact specifications for all leads\r\n    allLeads.forEach(leadName => {\r\n      const contacts = channelGroups[leadName] || [];\r\n      if (contacts.length > 0) {\r\n        const min = Math.min(...contacts);\r\n        const max = Math.max(...contacts);\r\n        newContactSpecs[leadName] = contacts.length > 1 ? `${min}-${max}` : `${min}`;\r\n      }\r\n    });\r\n\r\n    updateChannelSelection({\r\n      selected_leads: allLeads,\r\n      contact_specifications: newContactSpecs\r\n    });\r\n\r\n    // Expand all groups\r\n    setExpandedGroups(new Set(allLeads));\r\n  };\r\n\r\n  const handleDeselectAll = () => {\r\n    updateChannelSelection({\r\n      selected_leads: [],\r\n      contact_specifications: {}\r\n    });\r\n  };\r\n\r\n  const toggleGroupExpansion = (groupName: string) => {\r\n    const newExpanded = new Set(expandedGroups);\r\n    if (newExpanded.has(groupName)) {\r\n      newExpanded.delete(groupName);\r\n    } else {\r\n      newExpanded.add(groupName);\r\n    }\r\n    setExpandedGroups(newExpanded);\r\n  };\r\n\r\n  const parseContactSpecification = (spec: string): number[] => {\r\n    if (!spec) return [];\r\n    \r\n    const contacts: number[] = [];\r\n    const parts = spec.split(',');\r\n    \r\n    for (const part of parts) {\r\n      const trimmed = part.trim();\r\n      if (trimmed.includes('-')) {\r\n        const [start, end] = trimmed.split('-').map(n => parseInt(n.trim()));\r\n        if (!isNaN(start) && !isNaN(end)) {\r\n          for (let i = start; i <= end; i++) {\r\n            contacts.push(i);\r\n          }\r\n        }\r\n      } else {\r\n        const num = parseInt(trimmed);\r\n        if (!isNaN(num)) {\r\n          contacts.push(num);\r\n        }\r\n      }\r\n    }\r\n    \r\n    return [...new Set(contacts)].sort((a, b) => a - b);\r\n  };\r\n\r\n  const validateContactSpec = (leadName: string, spec: string): string | null => {\r\n    if (!spec.trim()) return \"Contact specification required\";\r\n    \r\n    const contacts = parseContactSpecification(spec);\r\n    const availableContacts = channelGroups[leadName] || [];\r\n    \r\n    if (contacts.length === 0) return \"Invalid contact specification format\";\r\n    \r\n    const invalidContacts = contacts.filter(c => !availableContacts.includes(c));\r\n    if (invalidContacts.length > 0) {\r\n      return `Invalid contacts: ${invalidContacts.join(', ')}. Available: ${availableContacts.join(', ')}`;\r\n    }\r\n    \r\n    return null;\r\n  };\r\n\r\n  const getTotalSelectedChannels = () => {\r\n    let total = 0;\r\n    channel_selection.selected_leads.forEach(leadName => {\r\n      const spec = channel_selection.contact_specifications[leadName] || '';\r\n      const contacts = parseContactSpecification(spec);\r\n      total += contacts.length;\r\n    });\r\n    return total;\r\n  };\r\n\r\n  const totalSelected = getTotalSelectedChannels();\r\n  const hasMinimumChannels = totalSelected >= 2;\r\n\r\n  return (\r\n    <div className=\"card-elegant p-6 slide-up\">\r\n      <div className=\"flex items-center justify-between mb-6\">\r\n        <div className=\"flex items-center gap-3\">\r\n          <div className=\"p-2 rounded-lg bg-gradient-to-r from-teal-100 to-cyan-100\">\r\n            <Layers className=\"w-5 h-5 text-teal-600\" />\r\n          </div>\r\n          <div>\r\n            <h2 className=\"text-lg font-semibold text-gray-900\">Channel Selection</h2>\r\n            <p className=\"text-sm text-gray-600\">Choose electrode contacts for HFO analysis</p>\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"flex items-center gap-3\">\r\n          <div className=\"text-right\">\r\n            <div className={`text-sm font-medium ${hasMinimumChannels ? 'text-green-600' : 'text-orange-600'}`}>\r\n              {totalSelected} channel{totalSelected !== 1 ? 's' : ''} selected\r\n            </div>\r\n            <div className=\"text-xs text-gray-500\">\r\n              {hasMinimumChannels ? 'Ready for analysis' : 'Minimum 2 required'}\r\n            </div>\r\n          </div>\r\n          <div className=\"flex flex-col gap-1\">\r\n            <button\r\n              onClick={handleSelectAll}\r\n              className=\"text-xs text-teal-600 hover:text-teal-700 font-medium px-2 py-1 rounded hover:bg-teal-50 transition-colors\"\r\n            >\r\n              Select All\r\n            </button>\r\n            <button\r\n              onClick={handleDeselectAll}\r\n              className=\"text-xs text-gray-600 hover:text-gray-700 font-medium px-2 py-1 rounded hover:bg-gray-50 transition-colors\"\r\n            >\r\n              Clear All\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {!hasMinimumChannels && (\r\n        <div className=\"mb-6 flex items-start gap-3 p-4 bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-lg\">\r\n          <AlertCircle className=\"w-5 h-5 text-orange-500 flex-shrink-0 mt-0.5\" />\r\n          <div>\r\n            <p className=\"text-sm font-medium text-orange-900 mb-1\">Minimum Channel Requirement</p>\r\n            <p className=\"text-sm text-orange-700\">\r\n              At least 2 channels must be selected to proceed with HFO analysis.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"mb-6 p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-gray-200\">\r\n        <h4 className=\"text-sm font-medium text-gray-900 mb-2\">Contact Specification Format</h4>\r\n        <div className=\"text-sm text-gray-600 space-y-1\">\r\n          <p>• Individual contacts: <code className=\"bg-white px-1 rounded text-xs\">1,3,5</code></p>\r\n          <p>• Ranges: <code className=\"bg-white px-1 rounded text-xs\">1-5</code> (contacts 1,2,3,4,5)</p>\r\n          <p>• Combined: <code className=\"bg-white px-1 rounded text-xs\">1-3,5,7-9</code></p>\r\n        </div>\r\n      </div>\r\n\r\n      {groupNames.length === 0 ? (\r\n        <div className=\"text-center py-8 text-gray-500\">\r\n          <Layers className=\"w-12 h-12 text-gray-300 mx-auto mb-3\" />\r\n          <p>No channel groups found in the selected file.</p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"space-y-3\">\r\n          {groupNames.map(groupName => {\r\n            const contacts = channelGroups[groupName] || [];\r\n            const isSelected = channel_selection.selected_leads.includes(groupName);\r\n            const isExpanded = expandedGroups.has(groupName);\r\n            const contactSpec = channel_selection.contact_specifications[groupName] || '';\r\n            const validationError = isSelected ? validateContactSpec(groupName, contactSpec) : null;\r\n            const selectedContacts = isSelected ? parseContactSpecification(contactSpec) : [];\r\n\r\n            return (\r\n              <div key={groupName} className=\"card-compact border-0 shadow-sm hover:shadow-md transition-all duration-200\">\r\n                <div \r\n                  className={`p-4 cursor-pointer rounded-lg transition-all ${\r\n                    isSelected \r\n                      ? 'bg-gradient-to-r from-teal-50 to-cyan-50 border-l-4 border-teal-400' \r\n                      : 'bg-gradient-to-r from-gray-50 to-gray-100 hover:from-indigo-50 hover:to-purple-50'\r\n                  }`}\r\n                  onClick={() => toggleGroupExpansion(groupName)}\r\n                >\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <button\r\n                        onClick={(e) => {\r\n                          e.stopPropagation();\r\n                          handleLeadToggle(groupName);\r\n                        }}\r\n                        className=\"flex items-center justify-center w-6 h-6 transition-all hover:scale-110\"\r\n                      >\r\n                        {isSelected ? (\r\n                          <CheckSquare className=\"w-5 h-5 text-teal-600\" />\r\n                        ) : (\r\n                          <Square className=\"w-5 h-5 text-gray-400 hover:text-gray-600\" />\r\n                        )}\r\n                      </button>\r\n                      <div>\r\n                        <div className=\"font-semibold text-gray-900 flex items-center gap-2\">\r\n                          <span>{groupName}</span>\r\n                          <span className=\"text-xs bg-white px-2 py-1 rounded-full text-gray-600\">\r\n                            {contacts.length} contacts\r\n                          </span>\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-600 mt-1\">\r\n                          Available: {contacts.join(', ')}\r\n                        </div>\r\n                        {isSelected && selectedContacts.length > 0 && (\r\n                          <div className=\"text-sm text-teal-700 mt-2 p-2 bg-white rounded-md\">\r\n                            <span className=\"font-medium\">Selected:</span> {selectedContacts.join(', ')} \r\n                            <span className=\"text-xs text-teal-600 ml-2\">({selectedContacts.length} channels)</span>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    <div className={`text-sm transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`}>\r\n                      <span className=\"text-gray-400\">▶</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {isExpanded && isSelected && (\r\n                  <div className=\"p-4 bg-white rounded-b-lg border-t border-gray-100 scale-in\">\r\n                    <label className=\"block text-sm font-semibold text-gray-800 mb-3\">\r\n                      Contact Specification for {groupName}\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={contactSpec}\r\n                      onChange={(e) => handleContactSpecChange(groupName, e.target.value)}\r\n                      placeholder={`e.g., 1-${contacts.length > 1 ? contacts[contacts.length - 1] : contacts[0]}`}\r\n                      className={`input-elegant ${\r\n                        validationError ? 'validation-error' : ''\r\n                      }`}\r\n                    />\r\n                    {validationError && (\r\n                      <p className=\"text-xs text-red-600 mt-2 p-2 bg-red-50 rounded-md border border-red-200\">\r\n                        {validationError}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"mt-6 p-4 bg-gradient-to-r from-teal-50 to-cyan-50 rounded-lg border border-teal-100\">\r\n        <h4 className=\"text-sm font-semibold text-teal-900 mb-2\">Analysis Guidelines</h4>\r\n        <div className=\"text-sm text-teal-800 space-y-1\">\r\n          <p>• Selected contacts will be processed for HFO detection</p>\r\n          <p>• Bipolar montage uses adjacent contact subtraction</p>\r\n          <p>• Choose contacts covering your region of interest</p>\r\n          <p>• Minimum 2 channels required for meaningful analysis</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChannelSelectionPanel;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,wBAAkC;;IACtC,MAAM,EAAE,KAAK,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD;IACtD,MAAM,EAAE,iBAAiB,EAAE,GAAG,MAAM,UAAU;IAC9C,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAEtE,kEAAkE;IAClE,MAAM,uBAAuB;QAC3B,IAAI,qBAAA,+BAAA,SAAU,cAAc,EAAE;YAC5B,OAAO,SAAS,cAAc;QAChC;QAEA,iDAAiD;QACjD,MAAM,SAAmC,CAAC;QAC1C,MAAM,cAAc;YAAC;YAAO;YAAO;YAAK;SAAI,EAAE,qBAAqB;QAEnE,IAAI,qBAAA,+BAAA,SAAU,QAAQ,EAAE;YACtB,SAAS,QAAQ,CAAC,OAAO,CAAC,CAAA;gBACxB,uCAAuC;gBACvC,MAAM,QAAQ,QAAQ,KAAK,CAAC;gBAC5B,IAAI,OAAO;oBACT,MAAM,GAAG,WAAW,WAAW,GAAG;oBAClC,IAAI,CAAC,YAAY,QAAQ,CAAC,UAAU,WAAW,KAAK;wBAClD,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;4BACtB,MAAM,CAAC,UAAU,GAAG,EAAE;wBACxB;wBACA,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS;oBAClC;gBACF;YACF;YAEA,yCAAyC;YACzC,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;gBAC1B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;YACnC;QACF;QAEA,OAAO;IACT;IAEA,MAAM,gBAAgB;IACtB,MAAM,aAAa,OAAO,IAAI,CAAC;IAE/B,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM,qBAAqB,WAAW,MAAM;sEAAC,CAAA,QAC3C,kBAAkB,cAAc,CAAC,QAAQ,CAAC;;YAE5C,kBAAkB,IAAI,IAAI;mBAAI;mBAAmB;aAAmB;QACtE;0CAAG;QAAC,kBAAkB,cAAc;KAAC;IAErC,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,kBAAkB,cAAc,CAAC,QAAQ,CAAC;QAC7D,IAAI;QACJ,IAAI,kBAAkB;YAAE,GAAG,kBAAkB,sBAAsB;QAAC;QAEpE,IAAI,YAAY;YACd,cAAc;YACd,mBAAmB,kBAAkB,cAAc,CAAC,MAAM,CAAC,CAAA,OAAQ,SAAS;YAC5E,OAAO,eAAe,CAAC,SAAS;QAClC,OAAO;YACL,WAAW;YACX,mBAAmB;mBAAI,kBAAkB,cAAc;gBAAE;aAAS;YAClE,gEAAgE;YAChE,MAAM,WAAW,aAAa,CAAC,SAAS,IAAI,EAAE;YAC9C,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,MAAM,MAAM,KAAK,GAAG,IAAI;gBACxB,MAAM,MAAM,KAAK,GAAG,IAAI;gBACxB,eAAe,CAAC,SAAS,GAAG,SAAS,MAAM,GAAG,IAAI,AAAC,GAAS,OAAP,KAAI,KAAO,OAAJ,OAAQ,AAAC,GAAM,OAAJ;YACzE;QACF;QAEA,uBAAuB;YACrB,gBAAgB;YAChB,wBAAwB;QAC1B;IACF;IAEA,MAAM,0BAA0B,CAAC,UAAkB;QACjD,uBAAuB;YACrB,wBAAwB;gBACtB,GAAG,kBAAkB,sBAAsB;gBAC3C,CAAC,SAAS,EAAE;YACd;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,WAAW;QACjB,MAAM,kBAA0C,CAAC;QAEjD,sDAAsD;QACtD,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,WAAW,aAAa,CAAC,SAAS,IAAI,EAAE;YAC9C,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,MAAM,MAAM,KAAK,GAAG,IAAI;gBACxB,MAAM,MAAM,KAAK,GAAG,IAAI;gBACxB,eAAe,CAAC,SAAS,GAAG,SAAS,MAAM,GAAG,IAAI,AAAC,GAAS,OAAP,KAAI,KAAO,OAAJ,OAAQ,AAAC,GAAM,OAAJ;YACzE;QACF;QAEA,uBAAuB;YACrB,gBAAgB;YAChB,wBAAwB;QAC1B;QAEA,oBAAoB;QACpB,kBAAkB,IAAI,IAAI;IAC5B;IAEA,MAAM,oBAAoB;QACxB,uBAAuB;YACrB,gBAAgB,EAAE;YAClB,wBAAwB,CAAC;QAC3B;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,YAAY;YAC9B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,kBAAkB;IACpB;IAEA,MAAM,4BAA4B,CAAC;QACjC,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,MAAM,WAAqB,EAAE;QAC7B,MAAM,QAAQ,KAAK,KAAK,CAAC;QAEzB,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,UAAU,KAAK,IAAI;YACzB,IAAI,QAAQ,QAAQ,CAAC,MAAM;gBACzB,MAAM,CAAC,OAAO,IAAI,GAAG,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,SAAS,EAAE,IAAI;gBAChE,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,MAAM;oBAChC,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;wBACjC,SAAS,IAAI,CAAC;oBAChB;gBACF;YACF,OAAO;gBACL,MAAM,MAAM,SAAS;gBACrB,IAAI,CAAC,MAAM,MAAM;oBACf,SAAS,IAAI,CAAC;gBAChB;YACF;QACF;QAEA,OAAO;eAAI,IAAI,IAAI;SAAU,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACnD;IAEA,MAAM,sBAAsB,CAAC,UAAkB;QAC7C,IAAI,CAAC,KAAK,IAAI,IAAI,OAAO;QAEzB,MAAM,WAAW,0BAA0B;QAC3C,MAAM,oBAAoB,aAAa,CAAC,SAAS,IAAI,EAAE;QAEvD,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;QAElC,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,kBAAkB,QAAQ,CAAC;QACzE,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,OAAO,AAAC,qBAA8D,OAA1C,gBAAgB,IAAI,CAAC,OAAM,iBAA4C,OAA7B,kBAAkB,IAAI,CAAC;QAC/F;QAEA,OAAO;IACT;IAEA,MAAM,2BAA2B;QAC/B,IAAI,QAAQ;QACZ,kBAAkB,cAAc,CAAC,OAAO,CAAC,CAAA;YACvC,MAAM,OAAO,kBAAkB,sBAAsB,CAAC,SAAS,IAAI;YACnE,MAAM,WAAW,0BAA0B;YAC3C,SAAS,SAAS,MAAM;QAC1B;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB;IACtB,MAAM,qBAAqB,iBAAiB;IAE5C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAIzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,AAAC,uBAAgF,OAA1D,qBAAqB,mBAAmB;;4CAC5E;4CAAc;4CAAS,kBAAkB,IAAI,MAAM;4CAAG;;;;;;;kDAEzD,6LAAC;wCAAI,WAAU;kDACZ,qBAAqB,uBAAuB;;;;;;;;;;;;0CAGjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;YAON,CAAC,oCACA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;0CACxD,6LAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAE;kDAAuB,6LAAC;wCAAK,WAAU;kDAAgC;;;;;;;;;;;;0CAC1E,6LAAC;;oCAAE;kDAAU,6LAAC;wCAAK,WAAU;kDAAgC;;;;;;oCAAU;;;;;;;0CACvE,6LAAC;;oCAAE;kDAAY,6LAAC;wCAAK,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;YAIlE,WAAW,MAAM,KAAK,kBACrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;kCAAE;;;;;;;;;;;yEAGL,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAA;oBACd,MAAM,WAAW,aAAa,CAAC,UAAU,IAAI,EAAE;oBAC/C,MAAM,aAAa,kBAAkB,cAAc,CAAC,QAAQ,CAAC;oBAC7D,MAAM,aAAa,eAAe,GAAG,CAAC;oBACtC,MAAM,cAAc,kBAAkB,sBAAsB,CAAC,UAAU,IAAI;oBAC3E,MAAM,kBAAkB,aAAa,oBAAoB,WAAW,eAAe;oBACnF,MAAM,mBAAmB,aAAa,0BAA0B,eAAe,EAAE;oBAEjF,qBACE,6LAAC;wBAAoB,WAAU;;0CAC7B,6LAAC;gCACC,WAAW,AAAC,gDAIX,OAHC,aACI,wEACA;gCAEN,SAAS,IAAM,qBAAqB;0CAEpC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,iBAAiB;oDACnB;oDACA,WAAU;8DAET,2BACC,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;iHAEvB,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAGtB,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAM;;;;;;8EACP,6LAAC;oEAAK,WAAU;;wEACb,SAAS,MAAM;wEAAC;;;;;;;;;;;;;sEAGrB,6LAAC;4DAAI,WAAU;;gEAA6B;gEAC9B,SAAS,IAAI,CAAC;;;;;;;wDAE3B,cAAc,iBAAiB,MAAM,GAAG,mBACvC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAc;;;;;;gEAAgB;gEAAE,iBAAiB,IAAI,CAAC;8EACtE,6LAAC;oEAAK,WAAU;;wEAA6B;wEAAE,iBAAiB,MAAM;wEAAC;;;;;;;;;;;;;;;;;;;;;;;;;sDAK/E,6LAAC;4CAAI,WAAW,AAAC,6CAA0E,OAA9B,aAAa,cAAc;sDACtF,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;4BAKrC,cAAc,4BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;;4CAAiD;4CACrC;;;;;;;kDAE7B,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,wBAAwB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAClE,aAAa,AAAC,WAA4E,OAAlE,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,GAAG,QAAQ,CAAC,EAAE;wCACzF,WAAW,AAAC,iBAEX,OADC,kBAAkB,qBAAqB;;;;;;oCAG1C,iCACC,6LAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;uBAhED;;;;;gBAuEd;;;;;;0BAIJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;0CACH,6LAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKb;GAzVM;;QACsC,uIAAA,CAAA,gBAAa;;;KADnD;uCA2VS", "debugId": null}}, {"offset": {"line": 4072, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/SettingsManager.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Save, Upload, RotateCcw, Settings, Download, FileText } from 'lucide-react';\r\nimport { SettingsData } from '@/types/eeg';\r\n\r\nconst SettingsManager: React.FC = () => {\r\n  const { state, loadParameters, resetParameters } = useParameters();\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [saveMessage, setSaveMessage] = useState<string | null>(null);\r\n\r\n  const generateSettingsKey = (): string => {\r\n    if (state.fileInfo) {\r\n      const filename = state.fileInfo.filename.replace(/\\.[^/.]+$/, \"\"); // Remove extension\r\n      return `hfo-settings-${filename}`;\r\n    }\r\n    return 'hfo-settings-default';\r\n  };\r\n\r\n  const saveSettings = () => {\r\n    try {\r\n      const settingsData: SettingsData = {\r\n        parameters: state.parameters,\r\n        timestamp: new Date().toISOString(),\r\n        filename: state.fileInfo?.filename || 'unknown'\r\n      };\r\n\r\n      const key = generateSettingsKey();\r\n      localStorage.setItem(key, JSON.stringify(settingsData));\r\n      \r\n      setSaveMessage('Settings saved successfully!');\r\n      setTimeout(() => setSaveMessage(null), 3000);\r\n      setIsMenuOpen(false);\r\n    } catch (error) {\r\n      setSaveMessage('Failed to save settings');\r\n      setTimeout(() => setSaveMessage(null), 3000);\r\n    }\r\n  };\r\n\r\n  const loadSettings = () => {\r\n    try {\r\n      const key = generateSettingsKey();\r\n      const savedData = localStorage.getItem(key);\r\n      \r\n      if (savedData) {\r\n        const settingsData: SettingsData = JSON.parse(savedData);\r\n        loadParameters(settingsData.parameters);\r\n        setSaveMessage('Settings loaded successfully!');\r\n        setTimeout(() => setSaveMessage(null), 3000);\r\n      } else {\r\n        setSaveMessage('No saved settings found for this file');\r\n        setTimeout(() => setSaveMessage(null), 3000);\r\n      }\r\n      setIsMenuOpen(false);\r\n    } catch (error) {\r\n      setSaveMessage('Failed to load settings');\r\n      setTimeout(() => setSaveMessage(null), 3000);\r\n    }\r\n  };\r\n\r\n  const exportSettings = () => {\r\n    try {\r\n      const settingsData: SettingsData = {\r\n        parameters: state.parameters,\r\n        timestamp: new Date().toISOString(),\r\n        filename: state.fileInfo?.filename || 'unknown'\r\n      };\r\n\r\n      const dataStr = JSON.stringify(settingsData, null, 2);\r\n      const dataBlob = new Blob([dataStr], { type: 'application/json' });\r\n      const url = URL.createObjectURL(dataBlob);\r\n      \r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = `hfo-settings-${state.fileInfo?.filename || 'export'}-${new Date().toISOString().split('T')[0]}.json`;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      URL.revokeObjectURL(url);\r\n\r\n      setSaveMessage('Settings exported successfully!');\r\n      setTimeout(() => setSaveMessage(null), 3000);\r\n      setIsMenuOpen(false);\r\n    } catch (error) {\r\n      setSaveMessage('Failed to export settings');\r\n      setTimeout(() => setSaveMessage(null), 3000);\r\n    }\r\n  };\r\n\r\n  const importSettings = () => {\r\n    const input = document.createElement('input');\r\n    input.type = 'file';\r\n    input.accept = '.json';\r\n    input.onchange = (e) => {\r\n      const file = (e.target as HTMLInputElement).files?.[0];\r\n      if (file) {\r\n        const reader = new FileReader();\r\n        reader.onload = (e) => {\r\n          try {\r\n            const content = e.target?.result as string;\r\n            const settingsData: SettingsData = JSON.parse(content);\r\n            loadParameters(settingsData.parameters);\r\n            setSaveMessage(`Settings imported from ${settingsData.filename}`);\r\n            setTimeout(() => setSaveMessage(null), 3000);\r\n          } catch (error) {\r\n            setSaveMessage('Failed to import settings - invalid file format');\r\n            setTimeout(() => setSaveMessage(null), 3000);\r\n          }\r\n        };\r\n        reader.readAsText(file);\r\n      }\r\n    };\r\n    input.click();\r\n    setIsMenuOpen(false);\r\n  };\r\n\r\n  const resetToDefaults = () => {\r\n    resetParameters();\r\n    setSaveMessage('Settings reset to defaults');\r\n    setTimeout(() => setSaveMessage(null), 3000);\r\n    setIsMenuOpen(false);\r\n  };\r\n\r\n  const hasSettings = () => {\r\n    const key = generateSettingsKey();\r\n    return localStorage.getItem(key) !== null;\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      <button\r\n        onClick={() => setIsMenuOpen(!isMenuOpen)}\r\n        className=\"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\"\r\n      >\r\n        <Settings className=\"w-4 h-4\" />\r\n        Settings\r\n      </button>\r\n\r\n      {isMenuOpen && (\r\n        <>\r\n          <div \r\n            className=\"fixed inset-0 z-10\" \r\n            onClick={() => setIsMenuOpen(false)}\r\n          />\r\n          <div className=\"absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-20\">\r\n            <div className=\"p-2\">\r\n              <div className=\"text-xs font-medium text-gray-500 px-2 py-1 mb-2\">\r\n                Local Settings ({state.fileInfo?.filename || 'No file'})\r\n              </div>\r\n              \r\n              <button\r\n                onClick={saveSettings}\r\n                className=\"w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors\"\r\n              >\r\n                <Save className=\"w-4 h-4\" />\r\n                Save Settings\r\n              </button>\r\n              \r\n              <button\r\n                onClick={loadSettings}\r\n                disabled={!hasSettings()}\r\n                className={`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md transition-colors ${\r\n                  hasSettings() \r\n                    ? 'text-gray-700 hover:bg-gray-100' \r\n                    : 'text-gray-400 cursor-not-allowed'\r\n                }`}\r\n              >\r\n                <Upload className=\"w-4 h-4\" />\r\n                Load Settings\r\n                {hasSettings() && <span className=\"text-xs text-green-600 ml-auto\">✓</span>}\r\n              </button>\r\n\r\n              <div className=\"border-t border-gray-200 my-2\" />\r\n              \r\n              <div className=\"text-xs font-medium text-gray-500 px-2 py-1 mb-2\">\r\n                Import/Export\r\n              </div>\r\n              \r\n              <button\r\n                onClick={exportSettings}\r\n                className=\"w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors\"\r\n              >\r\n                <Download className=\"w-4 h-4\" />\r\n                Export Settings\r\n              </button>\r\n              \r\n              <button\r\n                onClick={importSettings}\r\n                className=\"w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors\"\r\n              >\r\n                <FileText className=\"w-4 h-4\" />\r\n                Import Settings\r\n              </button>\r\n\r\n              <div className=\"border-t border-gray-200 my-2\" />\r\n              \r\n              <button\r\n                onClick={resetToDefaults}\r\n                className=\"w-full flex items-center gap-3 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors\"\r\n              >\r\n                <RotateCcw className=\"w-4 h-4\" />\r\n                Reset to Defaults\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </>\r\n      )}\r\n\r\n      {saveMessage && (\r\n        <div className=\"fixed top-4 right-4 z-50 bg-white border border-gray-200 rounded-md shadow-lg px-4 py-3\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className={`w-2 h-2 rounded-full ${\r\n              saveMessage.includes('successfully') || saveMessage.includes('imported') \r\n                ? 'bg-green-500' \r\n                : 'bg-red-500'\r\n            }`} />\r\n            <span className=\"text-sm text-gray-900\">{saveMessage}</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsManager;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAOA,MAAM,kBAA4B;QA6ID;;IA5I/B,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,sBAAsB;QAC1B,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,WAAW,MAAM,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,KAAK,mBAAmB;YACtF,OAAO,AAAC,gBAAwB,OAAT;QACzB;QACA,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,IAAI;gBAIU;YAHZ,MAAM,eAA6B;gBACjC,YAAY,MAAM,UAAU;gBAC5B,WAAW,IAAI,OAAO,WAAW;gBACjC,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,QAAQ,KAAI;YACxC;YAEA,MAAM,MAAM;YACZ,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;YAEzC,eAAe;YACf,WAAW,IAAM,eAAe,OAAO;YACvC,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,eAAe;YACf,WAAW,IAAM,eAAe,OAAO;QACzC;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM;YACZ,MAAM,YAAY,aAAa,OAAO,CAAC;YAEvC,IAAI,WAAW;gBACb,MAAM,eAA6B,KAAK,KAAK,CAAC;gBAC9C,eAAe,aAAa,UAAU;gBACtC,eAAe;gBACf,WAAW,IAAM,eAAe,OAAO;YACzC,OAAO;gBACL,eAAe;gBACf,WAAW,IAAM,eAAe,OAAO;YACzC;YACA,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,eAAe;YACf,WAAW,IAAM,eAAe,OAAO;QACzC;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;gBAIU,iBASoB;YAZhC,MAAM,eAA6B;gBACjC,YAAY,MAAM,UAAU;gBAC5B,WAAW,IAAI,OAAO,WAAW;gBACjC,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,QAAQ,KAAI;YACxC;YAEA,MAAM,UAAU,KAAK,SAAS,CAAC,cAAc,MAAM;YACnD,MAAM,WAAW,IAAI,KAAK;gBAAC;aAAQ,EAAE;gBAAE,MAAM;YAAmB;YAChE,MAAM,MAAM,IAAI,eAAe,CAAC;YAEhC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,AAAC,gBAAuD,OAAxC,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,QAAQ,KAAI,UAAS,KAA0C,OAAvC,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAC;YAC/G,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;YAEpB,eAAe;YACf,WAAW,IAAM,eAAe,OAAO;YACvC,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,eAAe;YACf,WAAW,IAAM,eAAe,OAAO;QACzC;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,IAAI,GAAG;QACb,MAAM,MAAM,GAAG;QACf,MAAM,QAAQ,GAAG,CAAC;gBACH;YAAb,MAAM,QAAO,SAAA,AAAC,EAAE,MAAM,CAAsB,KAAK,cAApC,6BAAA,MAAsC,CAAC,EAAE;YACtD,IAAI,MAAM;gBACR,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,IAAI;4BACc;wBAAhB,MAAM,WAAU,YAAA,EAAE,MAAM,cAAR,gCAAA,UAAU,MAAM;wBAChC,MAAM,eAA6B,KAAK,KAAK,CAAC;wBAC9C,eAAe,aAAa,UAAU;wBACtC,eAAe,AAAC,0BAA+C,OAAtB,aAAa,QAAQ;wBAC9D,WAAW,IAAM,eAAe,OAAO;oBACzC,EAAE,OAAO,OAAO;wBACd,eAAe;wBACf,WAAW,IAAM,eAAe,OAAO;oBACzC;gBACF;gBACA,OAAO,UAAU,CAAC;YACpB;QACF;QACA,MAAM,KAAK;QACX,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB;QACA,eAAe;QACf,WAAW,IAAM,eAAe,OAAO;QACvC,cAAc;IAChB;IAEA,MAAM,cAAc;QAClB,MAAM,MAAM;QACZ,OAAO,aAAa,OAAO,CAAC,SAAS;IACvC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,cAAc,CAAC;gBAC9B,WAAU;;kCAEV,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAY;;;;;;;YAIjC,4BACC;;kCACE,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,cAAc;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAmD;wCAC/C,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,QAAQ,KAAI;wCAAU;;;;;;;8CAGzD,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAI9B,6LAAC;oCACC,SAAS;oCACT,UAAU,CAAC;oCACX,WAAW,AAAC,iFAIX,OAHC,gBACI,oCACA;;sDAGN,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;wCAE7B,+BAAiB,6LAAC;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;8CAGrE,6LAAC;oCAAI,WAAU;;;;;;8CAEf,6LAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAIlE,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAIlC,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAIlC,6LAAC;oCAAI,WAAU;;;;;;8CAEf,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;YAQ1C,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAW,AAAC,wBAIhB,OAHC,YAAY,QAAQ,CAAC,mBAAmB,YAAY,QAAQ,CAAC,cACzD,iBACA;;;;;;sCAEN,6LAAC;4BAAK,WAAU;sCAAyB;;;;;;;;;;;;;;;;;;;;;;;AAMrD;GAxNM;;QAC+C,uIAAA,CAAA,gBAAa;;;KAD5D;uCA0NS", "debugId": null}}, {"offset": {"line": 4446, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Settings, Play, RotateCcw, Upload, CheckCircle, ArrowLeft, Activity } from 'lucide-react';\r\nimport HFOThresholdPanel from './HFOThresholdPanel';\r\nimport MontageSelectionPanel from './MontageSelectionPanel';\r\nimport FrequencyFilterPanel from './FrequencyFilterPanel';\r\nimport TimeSegmentPanel from './TimeSegmentPanel';\r\nimport ChannelSelectionPanel from './ChannelSelectionPanel';\r\nimport SettingsManager from './SettingsManager';\r\n\r\ninterface ParameterConfigurationPanelProps {\r\n  onStartAnalysis: () => void;\r\n}\r\n\r\nconst ParameterConfigurationPanel: React.FC<ParameterConfigurationPanelProps> = ({ onStartAnalysis }) => {\r\n  const { \r\n    state, \r\n    resetParameters, \r\n    validateParameters,\r\n    setStep \r\n  } = useParameters();\r\n\r\n  const [activeSection, setActiveSection] = useState<string>('thresholds');\r\n\r\n  const handleStartAnalysis = async () => {\r\n    const isValid = await validateParameters();\r\n    if (isValid) {\r\n      setStep('analysis');\r\n      onStartAnalysis();\r\n    }\r\n  };\r\n\r\n  const handleBackToFileSelection = () => {\r\n    setStep('file_selection');\r\n  };\r\n\r\n  if (!state.fileInfo) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-6\">\r\n        <div className=\"card-elegant p-12 text-center max-w-md\">\r\n          <Upload className=\"w-16 h-16 text-gray-300 mx-auto mb-6\" />\r\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">No File Selected</h3>\r\n          <p className=\"text-gray-600\">Please select an EDF file to configure analysis parameters.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const { fileInfo } = state;\r\n\r\n  const sections = [\r\n    { id: 'thresholds', title: 'HFO Thresholds', component: <HFOThresholdPanel /> },\r\n    { id: 'montage', title: 'Montage Configuration', component: <MontageSelectionPanel /> },\r\n    { id: 'frequency', title: 'Frequency Filters', component: <FrequencyFilterPanel /> },\r\n    { id: 'time', title: 'Time Segment', component: <TimeSegmentPanel /> },\r\n    { id: 'channels', title: 'Channel Selection', component: <ChannelSelectionPanel /> }\r\n  ];\r\n\r\n  const getSectionStatus = (sectionId: string) => {\r\n    // Basic validation for each section\r\n    switch (sectionId) {\r\n      case 'channels':\r\n        const totalChannels = state.parameters.channel_selection.selected_leads.length;\r\n        return totalChannels >= 2 ? 'completed' : 'incomplete';\r\n      default:\r\n        return 'completed'; // Assume other sections are complete with defaults\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\">\r\n      {/* Header */}\r\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\r\n        <div className=\"max-w-7xl mx-auto px-6 py-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-4\">\r\n              <button\r\n                onClick={handleBackToFileSelection}\r\n                className=\"btn-secondary flex items-center gap-2\"\r\n              >\r\n                <ArrowLeft className=\"w-4 h-4\" />\r\n                Back\r\n              </button>\r\n              <div className=\"flex items-center gap-3\">\r\n                <Activity className=\"w-8 h-8 text-indigo-600\" />\r\n                <div>\r\n                  <h1 className=\"text-xl font-bold text-gray-900\">HFO Analysis Configuration</h1>\r\n                  <p className=\"text-sm text-gray-600\">{fileInfo.filename}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"flex items-center gap-3\">\r\n              <SettingsManager />\r\n              <button\r\n                onClick={resetParameters}\r\n                className=\"btn-secondary flex items-center gap-2\"\r\n              >\r\n                <RotateCcw className=\"w-4 h-4\" />\r\n                Reset\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-7xl mx-auto p-6\">\r\n        {/* File Info Cards */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8\">\r\n          <div className=\"card-compact\">\r\n            <div className=\"text-xs text-gray-500 uppercase tracking-wide font-medium\">Duration</div>\r\n            <div className=\"text-lg font-semibold text-gray-900\">{fileInfo.duration_seconds.toFixed(1)}s</div>\r\n          </div>\r\n          <div className=\"card-compact\">\r\n            <div className=\"text-xs text-gray-500 uppercase tracking-wide font-medium\">Sampling Rate</div>\r\n            <div className=\"text-lg font-semibold text-gray-900\">{fileInfo.sampling_rate}Hz</div>\r\n          </div>\r\n          <div className=\"card-compact\">\r\n            <div className=\"text-xs text-gray-500 uppercase tracking-wide font-medium\">Channels</div>\r\n            <div className=\"text-lg font-semibold text-gray-900\">{fileInfo.channels.length}</div>\r\n          </div>\r\n          <div className=\"card-compact\">\r\n            <div className=\"text-xs text-gray-500 uppercase tracking-wide font-medium\">Max Frequency</div>\r\n            <div className=\"text-lg font-semibold text-gray-900\">{fileInfo.max_frequency}Hz</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Section Navigation */}\r\n        <div className=\"card-elegant p-6 mb-6\">\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            {sections.map((section) => {\r\n              const status = getSectionStatus(section.id);\r\n              const isActive = activeSection === section.id;\r\n              \r\n              return (\r\n                <button\r\n                  key={section.id}\r\n                  onClick={() => setActiveSection(section.id)}\r\n                  className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all ${\r\n                    isActive\r\n                      ? 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-md'\r\n                      : status === 'completed'\r\n                      ? 'bg-green-50 text-green-700 border border-green-200 hover:bg-green-100'\r\n                      : 'bg-gray-50 text-gray-700 border border-gray-200 hover:bg-gray-100'\r\n                  }`}\r\n                >\r\n                  {status === 'completed' && !isActive && (\r\n                    <CheckCircle className=\"w-4 h-4\" />\r\n                  )}\r\n                  <span className=\"text-sm\">{section.title}</span>\r\n                </button>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Active Section Content */}\r\n        <div className=\"fade-in\">\r\n          {sections.find(s => s.id === activeSection)?.component}\r\n        </div>\r\n\r\n        {/* Inline Validation Status */}\r\n        {state.validationErrors.length > 0 && (\r\n          <div className=\"card-elegant validation-error p-4 mb-6\">\r\n            <div className=\"flex items-start gap-3\">\r\n              <div className=\"text-red-500 mt-1\">⚠</div>\r\n              <div>\r\n                <h4 className=\"font-medium text-red-900 mb-2\">Configuration Issues</h4>\r\n                <ul className=\"space-y-1\">\r\n                  {state.validationErrors.map((error, index) => (\r\n                    <li key={index} className=\"text-sm text-red-700\">\r\n                      • {error.message}\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Progress Summary */}\r\n        <div className=\"card-elegant p-6 mb-8\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Configuration Progress</h3>\r\n              <div className=\"flex items-center gap-4 text-sm text-gray-600\">\r\n                <span>\r\n                  Sections: {sections.filter(s => getSectionStatus(s.id) === 'completed').length}/{sections.length}\r\n                </span>\r\n                <span>•</span>\r\n                <span className={state.canStartAnalysis ? 'status-success' : 'status-warning'}>\r\n                  {state.canStartAnalysis ? 'Ready to analyze' : 'Configuration incomplete'}\r\n                </span>\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"flex items-center gap-3\">\r\n              {state.isValidating && (\r\n                <div className=\"flex items-center gap-2 text-sm text-gray-600\">\r\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600\"></div>\r\n                  Validating...\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Floating Action Button */}\r\n      <button\r\n        onClick={handleStartAnalysis}\r\n        disabled={!state.canStartAnalysis || state.isValidating}\r\n        className={`fab ${\r\n          state.canStartAnalysis && !state.isValidating\r\n            ? ''\r\n            : 'fab:disabled'\r\n        }`}\r\n      >\r\n        <Play className=\"w-5 h-5\" />\r\n        Start Analysis\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ParameterConfigurationPanel;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAgBA,MAAM,8BAA0E;QAAC,EAAE,eAAe,EAAE;QAgJzF;;IA/IT,MAAM,EACJ,KAAK,EACL,eAAe,EACf,kBAAkB,EAClB,OAAO,EACR,GAAG,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD;IAEhB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,MAAM,sBAAsB;QAC1B,MAAM,UAAU,MAAM;QACtB,IAAI,SAAS;YACX,QAAQ;YACR;QACF;IACF;IAEA,MAAM,4BAA4B;QAChC,QAAQ;IACV;IAEA,IAAI,CAAC,MAAM,QAAQ,EAAE;QACnB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,MAAM,WAAW;QACf;YAAE,IAAI;YAAc,OAAO;YAAkB,yBAAW,6LAAC,oKAAA,CAAA,UAAiB;;;;;QAAI;QAC9E;YAAE,IAAI;YAAW,OAAO;YAAyB,yBAAW,6LAAC,wKAAA,CAAA,UAAqB;;;;;QAAI;QACtF;YAAE,IAAI;YAAa,OAAO;YAAqB,yBAAW,6LAAC,uKAAA,CAAA,UAAoB;;;;;QAAI;QACnF;YAAE,IAAI;YAAQ,OAAO;YAAgB,yBAAW,6LAAC,mKAAA,CAAA,UAAgB;;;;;QAAI;QACrE;YAAE,IAAI;YAAY,OAAO;YAAqB,yBAAW,6LAAC,wKAAA,CAAA,UAAqB;;;;;QAAI;KACpF;IAED,MAAM,mBAAmB,CAAC;QACxB,oCAAoC;QACpC,OAAQ;YACN,KAAK;gBACH,MAAM,gBAAgB,MAAM,UAAU,CAAC,iBAAiB,CAAC,cAAc,CAAC,MAAM;gBAC9E,OAAO,iBAAiB,IAAI,cAAc;YAC5C;gBACE,OAAO,aAAa,mDAAmD;QAC3E;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAkC;;;;;;kEAChD,6LAAC;wDAAE,WAAU;kEAAyB,SAAS,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;0CAK7D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,kKAAA,CAAA,UAAe;;;;;kDAChB,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA4D;;;;;;kDAC3E,6LAAC;wCAAI,WAAU;;4CAAuC,SAAS,gBAAgB,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAE7F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA4D;;;;;;kDAC3E,6LAAC;wCAAI,WAAU;;4CAAuC,SAAS,aAAa;4CAAC;;;;;;;;;;;;;0CAE/E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA4D;;;;;;kDAC3E,6LAAC;wCAAI,WAAU;kDAAuC,SAAS,QAAQ,CAAC,MAAM;;;;;;;;;;;;0CAEhF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA4D;;;;;;kDAC3E,6LAAC;wCAAI,WAAU;;4CAAuC,SAAS,aAAa;4CAAC;;;;;;;;;;;;;;;;;;;kCAKjF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,SAAS,iBAAiB,QAAQ,EAAE;gCAC1C,MAAM,WAAW,kBAAkB,QAAQ,EAAE;gCAE7C,qBACE,6LAAC;oCAEC,SAAS,IAAM,iBAAiB,QAAQ,EAAE;oCAC1C,WAAW,AAAC,2EAMX,OALC,WACI,wEACA,WAAW,cACX,0EACA;;wCAGL,WAAW,eAAe,CAAC,0BAC1B,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDAEzB,6LAAC;4CAAK,WAAU;sDAAW,QAAQ,KAAK;;;;;;;mCAbnC,QAAQ,EAAE;;;;;4BAgBrB;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;mCACZ,iBAAA,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,4BAA5B,qCAAA,eAA4C,SAAS;;;;;;oBAIvD,MAAM,gBAAgB,CAAC,MAAM,GAAG,mBAC/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAoB;;;;;;8CACnC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,6LAAC;4CAAG,WAAU;sDACX,MAAM,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,sBAClC,6LAAC;oDAAe,WAAU;;wDAAuB;wDAC5C,MAAM,OAAO;;mDADT;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAK;wDACO,SAAS,MAAM,CAAC,CAAA,IAAK,iBAAiB,EAAE,EAAE,MAAM,aAAa,MAAM;wDAAC;wDAAE,SAAS,MAAM;;;;;;;8DAElG,6LAAC;8DAAK;;;;;;8DACN,6LAAC;oDAAK,WAAW,MAAM,gBAAgB,GAAG,mBAAmB;8DAC1D,MAAM,gBAAgB,GAAG,qBAAqB;;;;;;;;;;;;;;;;;;8CAKrD,6LAAC;oCAAI,WAAU;8CACZ,MAAM,YAAY,kBACjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;4CAAuE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlG,6LAAC;gBACC,SAAS;gBACT,UAAU,CAAC,MAAM,gBAAgB,IAAI,MAAM,YAAY;gBACvD,WAAW,AAAC,OAIX,OAHC,MAAM,gBAAgB,IAAI,CAAC,MAAM,YAAY,GACzC,KACA;;kCAGN,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAY;;;;;;;;;;;;;AAKpC;GAjNM;;QAMA,uIAAA,CAAA,gBAAa;;;KANb;uCAmNS", "debugId": null}}, {"offset": {"line": 5088, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Activity } from \"lucide-react\";\nimport EEG<PERSON>iewer from \"@/components/EEGViewer\";\nimport FileUploadCard from \"@/components/FileUploadCard\";\nimport ParameterConfigurationPanel from \"@/components/ParameterConfiguration\";\nimport { WebSocketProvider } from \"@/contexts/WebSocketContext\";\nimport { ParameterProvider, useParameters } from \"@/contexts/ParameterContext\";\nimport { FileInfo } from \"@/types/eeg\";\n\nfunction AppContent() {\n  const { state, updateFileInfo, setStep } = useParameters();\n  const [error, setError] = useState(\"\");\n\n  const handleFileSelect = async (filepath: string) => {\n    if (!filepath) {\n      setError(\"Please enter a file path\");\n      return;\n    }\n\n    try {\n      setError(\"\");\n      \n      // Call the backend to validate the file and get file info\n      const response = await fetch(\"http://localhost:8000/api/analyze\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ filepath }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || \"Failed to load file\");\n      }\n\n      const data = await response.json();\n      \n      // Extract file info from response\n      const fileInfo: FileInfo = {\n        filename: filepath.split('/').pop() || filepath,\n        filepath: filepath,\n        start_date: data.file_info.start_date,\n        start_time: data.file_info.start_time,\n        end_date: calculateEndDate(data.file_info.start_date, data.file_info.start_time, data.file_info.duration_seconds),\n        end_time: calculateEndTime(data.file_info.start_time, data.file_info.duration_seconds),\n        sampling_rate: data.file_info.sampling_rate,\n        max_frequency: data.file_info.sampling_rate / 3,\n        channels: data.file_info.channels,\n        channel_groups: extractChannelGroups(data.file_info.channels),\n        duration_seconds: data.file_info.duration_seconds\n      };\n\n      updateFileInfo(fileInfo);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"Failed to load file\");\n    }\n  };\n\n  const handleStartAnalysis = () => {\n    setStep('analysis');\n  };\n\n  // Helper functions for date/time calculations\n  const calculateEndDate = (startDate: string, startTime: string, durationSeconds: number): string => {\n    try {\n      // Convert dd.mm.yy to Date\n      const [day, month, year] = startDate.split('.');\n      const [hour, minute, second] = startTime.split(/[.:]/).map(n => parseInt(n));\n      \n      const startDateTime = new Date(2000 + parseInt(year), parseInt(month) - 1, parseInt(day), hour, minute, second);\n      const endDateTime = new Date(startDateTime.getTime() + durationSeconds * 1000);\n      \n      return endDateTime.toLocaleDateString('en-GB', { \n        day: '2-digit', \n        month: '2-digit', \n        year: '2-digit' \n      }).replace(/\\//g, '.');\n    } catch {\n      return startDate; // Fallback\n    }\n  };\n\n  const calculateEndTime = (startTime: string, durationSeconds: number): string => {\n    try {\n      const [hour, minute, second] = startTime.split(/[.:]/).map(n => parseInt(n));\n      const startDateTime = new Date();\n      startDateTime.setHours(hour, minute, second);\n      \n      const endDateTime = new Date(startDateTime.getTime() + durationSeconds * 1000);\n      \n      return endDateTime.toTimeString().slice(0, 8);\n    } catch {\n      return startTime; // Fallback\n    }\n  };\n\n  const extractChannelGroups = (channels: string[]): Record<string, number[]> => {\n    const groups: Record<string, number[]> = {};\n    const excludeList = ['EKG', 'REF', 'E', 'C'];\n\n    channels.forEach(channel => {\n      const match = channel.match(/^(?:POL |P )?(\\w+?)(\\d+)$/);\n      if (match) {\n        const [, groupName, contactNum] = match;\n        if (!excludeList.includes(groupName.toUpperCase())) {\n          if (!groups[groupName]) {\n            groups[groupName] = [];\n          }\n          groups[groupName].push(parseInt(contactNum));\n        }\n      }\n    });\n\n    // Sort contact numbers within each group\n    Object.keys(groups).forEach(group => {\n      groups[group].sort((a, b) => a - b);\n    });\n\n    return groups;\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\">\n      {state.step === 'file_selection' && (\n        <main className=\"flex items-center justify-center min-h-screen px-6 py-8\">\n          <div className=\"max-w-4xl w-full\">\n            <div className=\"text-center mb-12 fade-in\">\n              <div className=\"flex items-center justify-center gap-4 mb-6\">\n                <div className=\"p-3 rounded-xl bg-gradient-to-r from-indigo-100 to-purple-100\">\n                  <Activity className=\"w-10 h-10 text-indigo-600\" />\n                </div>\n                <div className=\"text-left\">\n                  <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">Biormika HFO Detector</h1>\n                  <p className=\"text-lg text-gray-600\">\n                    High-Frequency Oscillation Detection and Analysis\n                  </p>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"slide-up\">\n              <FileUploadCard \n                onFileSelect={() => {}} // Not used in new flow\n                onStartAnalysis={() => {}} // Not used in new flow\n                error={error}\n                onFileValidated={handleFileSelect}\n              />\n            </div>\n          </div>\n        </main>\n      )}\n\n      {state.step === 'parameter_configuration' && (\n        <ParameterConfigurationPanel onStartAnalysis={handleStartAnalysis} />\n      )}\n\n      {state.step === 'analysis' && (\n        <WebSocketProvider>\n          <EEGViewer />\n        </WebSocketProvider>\n      )}\n    </div>\n  );\n}\n\nexport default function Home() {\n  return (\n    <ParameterProvider>\n      <AppContent />\n    </ParameterProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAWA,SAAS;;IACP,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,gBAAa,AAAD;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,UAAU;YACb,SAAS;YACT;QACF;QAEA,IAAI;YACF,SAAS;YAET,0DAA0D;YAC1D,MAAM,WAAW,MAAM,MAAM,qCAAqC;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;YACtC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,kCAAkC;YAClC,MAAM,WAAqB;gBACzB,UAAU,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;gBACvC,UAAU;gBACV,YAAY,KAAK,SAAS,CAAC,UAAU;gBACrC,YAAY,KAAK,SAAS,CAAC,UAAU;gBACrC,UAAU,iBAAiB,KAAK,SAAS,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC,gBAAgB;gBAChH,UAAU,iBAAiB,KAAK,SAAS,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC,gBAAgB;gBACrF,eAAe,KAAK,SAAS,CAAC,aAAa;gBAC3C,eAAe,KAAK,SAAS,CAAC,aAAa,GAAG;gBAC9C,UAAU,KAAK,SAAS,CAAC,QAAQ;gBACjC,gBAAgB,qBAAqB,KAAK,SAAS,CAAC,QAAQ;gBAC5D,kBAAkB,KAAK,SAAS,CAAC,gBAAgB;YACnD;YAEA,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,sBAAsB;QAC1B,QAAQ;IACV;IAEA,8CAA8C;IAC9C,MAAM,mBAAmB,CAAC,WAAmB,WAAmB;QAC9D,IAAI;YACF,2BAA2B;YAC3B,MAAM,CAAC,KAAK,OAAO,KAAK,GAAG,UAAU,KAAK,CAAC;YAC3C,MAAM,CAAC,MAAM,QAAQ,OAAO,GAAG,UAAU,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAA,IAAK,SAAS;YAEzE,MAAM,gBAAgB,IAAI,KAAK,OAAO,SAAS,OAAO,SAAS,SAAS,GAAG,SAAS,MAAM,MAAM,QAAQ;YACxG,MAAM,cAAc,IAAI,KAAK,cAAc,OAAO,KAAK,kBAAkB;YAEzE,OAAO,YAAY,kBAAkB,CAAC,SAAS;gBAC7C,KAAK;gBACL,OAAO;gBACP,MAAM;YACR,GAAG,OAAO,CAAC,OAAO;QACpB,EAAE,UAAM;YACN,OAAO,WAAW,WAAW;QAC/B;IACF;IAEA,MAAM,mBAAmB,CAAC,WAAmB;QAC3C,IAAI;YACF,MAAM,CAAC,MAAM,QAAQ,OAAO,GAAG,UAAU,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAA,IAAK,SAAS;YACzE,MAAM,gBAAgB,IAAI;YAC1B,cAAc,QAAQ,CAAC,MAAM,QAAQ;YAErC,MAAM,cAAc,IAAI,KAAK,cAAc,OAAO,KAAK,kBAAkB;YAEzE,OAAO,YAAY,YAAY,GAAG,KAAK,CAAC,GAAG;QAC7C,EAAE,UAAM;YACN,OAAO,WAAW,WAAW;QAC/B;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,SAAmC,CAAC;QAC1C,MAAM,cAAc;YAAC;YAAO;YAAO;YAAK;SAAI;QAE5C,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,QAAQ,QAAQ,KAAK,CAAC;YAC5B,IAAI,OAAO;gBACT,MAAM,GAAG,WAAW,WAAW,GAAG;gBAClC,IAAI,CAAC,YAAY,QAAQ,CAAC,UAAU,WAAW,KAAK;oBAClD,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;wBACtB,MAAM,CAAC,UAAU,GAAG,EAAE;oBACxB;oBACA,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS;gBAClC;YACF;QACF;QAEA,yCAAyC;QACzC,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QACnC;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,MAAM,IAAI,KAAK,kCACd,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAO3C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;gCACb,cAAc,KAAO;gCACrB,iBAAiB,KAAO;gCACxB,OAAO;gCACP,iBAAiB;;;;;;;;;;;;;;;;;;;;;;YAO1B,MAAM,IAAI,KAAK,2CACd,6LAAC,wJAAA,CAAA,UAA2B;gBAAC,iBAAiB;;;;;;YAG/C,MAAM,IAAI,KAAK,4BACd,6LAAC,uIAAA,CAAA,oBAAiB;0BAChB,cAAA,6LAAC,2IAAA,CAAA,UAAS;;;;;;;;;;;;;;;;AAKpB;GA3JS;;QACoC,uIAAA,CAAA,gBAAa;;;KADjD;AA6JM,SAAS;IACtB,qBACE,6LAAC,uIAAA,CAAA,oBAAiB;kBAChB,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}]}