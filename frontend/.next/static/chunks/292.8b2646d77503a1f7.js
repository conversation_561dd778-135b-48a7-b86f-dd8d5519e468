(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[292],{284:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>s});var n=r(5155),o=r(2115),i=r(1514);let f=o.memo(t=>{let{channelName:e,data:r,timeWindow:f,samplingRate:s,height:a=80,showHFOMarkers:u=!1,hfoEvents:l=[]}=t,h=(0,o.useMemo)(()=>{if(!r||0===r.length)return[];let t=r.map((t,e)=>e/s),n=Math.floor(f[0]*s),o=Math.floor(f[1]*s),i=[{x:t.slice(n,o),y:r.slice(n,o),type:"scatter",mode:"lines",name:e,line:{color:"#000000",width:.8},hovertemplate:"Time: %{x:.2f}s<br>Amplitude: %{y:.2f}μV<extra></extra>"}];if(u){let t=l.filter(t=>t.channel===e&&t.start_time>=f[0]&&t.start_time<=f[1]);t.length>0&&i.push({x:t.map(t=>t.start_time),y:t.map(()=>0),type:"scatter",mode:"markers",name:"HFO",marker:{color:"#ef4444",size:6,symbol:"circle"},showlegend:!1,hovertemplate:"HFO Event<br>Time: %{x:.2f}s<extra></extra>"})}return i},[r,e,f,s,u,l]);return(0,n.jsx)("div",{className:"border-b border-gray-100 hover:bg-gray-50 transition-colors",children:(0,n.jsx)(i.A,{data:h,layout:{xaxis:{range:f,showgrid:!1,showticklabels:!1,zeroline:!1,showline:!1},yaxis:{showgrid:!1,showticklabels:!1,zeroline:!1,showline:!1,autorange:!0},height:a,margin:{l:80,r:10,t:5,b:5},hovermode:"x",showlegend:!1,plot_bgcolor:"#ffffff",paper_bgcolor:"#ffffff",annotations:[{x:0,y:.5,xref:"paper",yref:"paper",text:e,showarrow:!1,xanchor:"right",xshift:-10,font:{size:11,color:"#4a5568",family:"var(--font-sans), system-ui, -apple-system, sans-serif"}}]},config:{displayModeBar:!1,responsive:!0,staticPlot:!1},style:{width:"100%",height:"".concat(a,"px")}})})}),s=t=>{let{channelData:e,visibleChannels:r,timeWindow:i,samplingRate:s,showHFOMarkers:a=!1,hfoEvents:u=[],channelHeight:l=80}=t,h=(0,o.useRef)(null),c=t=>{h.current&&t.shiftKey&&(t.preventDefault(),h.current.scrollLeft+=t.deltaY)};return((0,o.useEffect)(()=>{let t=h.current;if(t)return t.addEventListener("wheel",c,{passive:!1}),()=>{t.removeEventListener("wheel",c)}},[]),0===r.length)?(0,n.jsx)("div",{className:"flex-1 flex items-center justify-center text-gray-500",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"text-sm",children:"No channels selected"}),(0,n.jsx)("p",{className:"text-xs mt-1",children:"Select channels from the panel on the right"})]})}):(0,n.jsxs)("div",{ref:h,className:"flex-1 overflow-y-auto overflow-x-hidden bg-white",style:{maxHeight:"calc(100vh - 128px)"},children:[(0,n.jsx)("div",{className:"min-w-full",children:r.map(t=>{let r=e[t];return r&&0!==r.length?(0,n.jsx)(f,{channelName:t,data:r,timeWindow:i,samplingRate:s,height:l,showHFOMarkers:a,hfoEvents:u},t):null})}),(0,n.jsx)("div",{className:"p-4 text-center text-xs text-gray-500",children:(0,n.jsx)("p",{children:"Tip: Use scroll to navigate vertically, Shift+Scroll for horizontal navigation"})})]})}},1514:(t,e,r)=>{"use strict";e.A=void 0;var n=i(r(2291)),o=i(r(6014));function i(t){return t&&t.__esModule?t:{default:t}}e.A=(0,n.default)(o.default)},2291:(t,e,r)=>{"use strict";function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=function(e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");p.prototype=Object.create(e&&e.prototype,{constructor:{value:p,writable:!0,configurable:!0}}),Object.defineProperty(p,"prototype",{writable:!1}),e&&s(p,e);var r,i,f=(r=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,e=u(p);return t=r?Reflect.construct(e,arguments,u(this).constructor):e.apply(this,arguments),function(t,e){if(e&&("object"===n(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return a(t)}(this,t)});function p(t){var e;if(!(this instanceof p))throw TypeError("Cannot call a class as a function");return(e=f.call(this,t)).p=Promise.resolve(),e.resizeHandler=null,e.handlers={},e.syncWindowResize=e.syncWindowResize.bind(a(e)),e.syncEventHandlers=e.syncEventHandlers.bind(a(e)),e.attachUpdateEvents=e.attachUpdateEvents.bind(a(e)),e.getRef=e.getRef.bind(a(e)),e.handleUpdate=e.handleUpdate.bind(a(e)),e.figureCallback=e.figureCallback.bind(a(e)),e.updatePlotly=e.updatePlotly.bind(a(e)),e}return i=[{key:"updatePlotly",value:function(e,r,n){var o=this;this.p=this.p.then(function(){if(!o.unmounting){if(!o.el)throw Error("Missing element reference");return t.react(o.el,{data:o.props.data,layout:o.props.layout,config:o.props.config,frames:o.props.frames})}}).then(function(){!o.unmounting&&(o.syncWindowResize(e),o.syncEventHandlers(),o.figureCallback(r),n&&o.attachUpdateEvents())}).catch(function(t){o.props.onError&&o.props.onError(t)})}},{key:"componentDidMount",value:function(){this.unmounting=!1,this.updatePlotly(!0,this.props.onInitialized,!0)}},{key:"componentDidUpdate",value:function(t){this.unmounting=!1;var e=t.frames&&t.frames.length?t.frames.length:0,r=this.props.frames&&this.props.frames.length?this.props.frames.length:0,n=t.layout!==this.props.layout||t.data!==this.props.data||t.config!==this.props.config||r!==e,o=void 0!==t.revision,i=t.revision!==this.props.revision;(n||o&&(!o||i))&&this.updatePlotly(!1,this.props.onUpdate,!1)}},{key:"componentWillUnmount",value:function(){this.unmounting=!0,this.figureCallback(this.props.onPurge),this.resizeHandler&&c&&(window.removeEventListener("resize",this.resizeHandler),this.resizeHandler=null),this.removeUpdateEvents(),t.purge(this.el)}},{key:"attachUpdateEvents",value:function(){var t=this;this.el&&this.el.removeListener&&h.forEach(function(e){t.el.on(e,t.handleUpdate)})}},{key:"removeUpdateEvents",value:function(){var t=this;this.el&&this.el.removeListener&&h.forEach(function(e){t.el.removeListener(e,t.handleUpdate)})}},{key:"handleUpdate",value:function(){this.figureCallback(this.props.onUpdate)}},{key:"figureCallback",value:function(t){if("function"==typeof t){var e=this.el;t({data:e.data,layout:e.layout,frames:this.el._transitionData?this.el._transitionData._frames:null},this.el)}}},{key:"syncWindowResize",value:function(e){var r=this;c&&(this.props.useResizeHandler&&!this.resizeHandler?(this.resizeHandler=function(){return t.Plots.resize(r.el)},window.addEventListener("resize",this.resizeHandler),e&&this.resizeHandler()):!this.props.useResizeHandler&&this.resizeHandler&&(window.removeEventListener("resize",this.resizeHandler),this.resizeHandler=null))}},{key:"getRef",value:function(t){this.el=t,this.props.debug&&c&&(window.gd=this.el)}},{key:"syncEventHandlers",value:function(){var t=this;l.forEach(function(e){var r=t.props["on"+e],n=t.handlers[e],o=!!n;r&&!o?t.addEventHandler(e,r):!r&&o?t.removeEventHandler(e):r&&o&&r!==n&&(t.removeEventHandler(e),t.addEventHandler(e,r))})}},{key:"addEventHandler",value:function(t,e){this.handlers[t]=e,this.el.on(this.getPlotlyEventName(t),this.handlers[t])}},{key:"removeEventHandler",value:function(t){this.el.removeListener(this.getPlotlyEventName(t),this.handlers[t]),delete this.handlers[t]}},{key:"getPlotlyEventName",value:function(t){return"plotly_"+t.toLowerCase()}},{key:"render",value:function(){return o.default.createElement("div",{id:this.props.divId,style:this.props.style,ref:this.getRef,className:this.props.className})}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}(p.prototype,i),Object.defineProperty(p,"prototype",{writable:!1}),p}(o.Component);return e.propTypes={data:i.default.arrayOf(i.default.object),config:i.default.object,layout:i.default.object,frames:i.default.arrayOf(i.default.object),revision:i.default.number,onInitialized:i.default.func,onPurge:i.default.func,onError:i.default.func,onUpdate:i.default.func,debug:i.default.bool,style:i.default.object,className:i.default.string,useResizeHandler:i.default.bool,divId:i.default.string},l.forEach(function(t){e.propTypes["on"+t]=i.default.func}),e.defaultProps={debug:!1,useResizeHandler:!1,data:[],style:{position:"relative",display:"inline-block"}},e};var o=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!==n(t)&&"function"!=typeof t)return{default:t};var r=f(e);if(r&&r.has(t))return r.get(t);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if("default"!==s&&Object.prototype.hasOwnProperty.call(t,s)){var a=i?Object.getOwnPropertyDescriptor(t,s):null;a&&(a.get||a.set)?Object.defineProperty(o,s,a):o[s]=t[s]}return o.default=t,r&&r.set(t,o),o}(r(2115)),i=function(t){return t&&t.__esModule?t:{default:t}}(r(8637));function f(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,r=new WeakMap;return(f=function(t){return t?r:e})(t)}function s(t,e){return(s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function a(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function u(t){return(u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var l=["AfterExport","AfterPlot","Animated","AnimatingFrame","AnimationInterrupted","AutoSize","BeforeExport","BeforeHover","ButtonClicked","Click","ClickAnnotation","Deselect","DoubleClick","Framework","Hover","LegendClick","LegendDoubleClick","Relayout","Relayouting","Restyle","Redraw","Selected","Selecting","SliderChange","SliderEnd","SliderStart","SunburstClick","Transitioning","TransitionInterrupted","Unhover","WebGlContextLost"],h=["plotly_restyle","plotly_redraw","plotly_relayout","plotly_relayouting","plotly_doubleclick","plotly_animated","plotly_sunburstclick"],c="undefined"!=typeof window},2948:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},8637:(t,e,r)=>{t.exports=r(9399)()},9399:(t,e,r)=>{"use strict";var n=r(2948);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,f){if(f!==n){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},9641:t=>{!function(){var e={675:function(t,e){"use strict";e.byteLength=function(t){var e=a(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,i=a(t),f=i[0],s=i[1],u=new o((f+s)*3/4-s),l=0,h=s>0?f-4:f;for(r=0;r<h;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],u[l++]=e>>16&255,u[l++]=e>>8&255,u[l++]=255&e;return 2===s&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,u[l++]=255&e),1===s&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,u[l++]=e>>8&255,u[l++]=255&e),u},e.fromByteArray=function(t){for(var e,n=t.length,o=n%3,i=[],f=0,s=n-o;f<s;f+=16383)i.push(function(t,e,n){for(var o,i=[],f=e;f<n;f+=3)o=(t[f]<<16&0xff0000)+(t[f+1]<<8&65280)+(255&t[f+2]),i.push(r[o>>18&63]+r[o>>12&63]+r[o>>6&63]+r[63&o]);return i.join("")}(t,f,f+16383>s?s:f+16383));return 1===o?i.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===o&&i.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),i.join("")};for(var r=[],n=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",f=0,s=i.length;f<s;++f)r[f]=i[f],n[i.charCodeAt(f)]=f;function a(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(t,e,r){"use strict";var n=r(675),o=r(783),i="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function f(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,s.prototype),e}function s(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return l(t)}return a(t,e,r)}function a(t,e,r){if("string"==typeof t){var n=t,o=e;if(("string"!=typeof o||""===o)&&(o="utf8"),!s.isEncoding(o))throw TypeError("Unknown encoding: "+o);var i=0|p(n,o),a=f(i),u=a.write(n,o);return u!==i&&(a=a.slice(0,u)),a}if(ArrayBuffer.isView(t))return h(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(T(t,ArrayBuffer)||t&&T(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(T(t,SharedArrayBuffer)||t&&T(t.buffer,SharedArrayBuffer)))return function(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),s.prototype),n}(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var l=t.valueOf&&t.valueOf();if(null!=l&&l!==t)return s.from(l,e,r);var y=function(t){if(s.isBuffer(t)){var e=0|c(t.length),r=f(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?f(0):h(t):"Buffer"===t.type&&Array.isArray(t.data)?h(t.data):void 0}(t);if(y)return y;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return s.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function u(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function l(t){return u(t),f(t<0?0:0|c(t))}function h(t){for(var e=t.length<0?0:0|c(t.length),r=f(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}e.Buffer=s,e.SlowBuffer=function(t){return+t!=t&&(t=0),s.alloc(+t)},e.INSPECT_MAX_BYTES=50,e.kMaxLength=0x7fffffff,s.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),s.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}}),s.poolSize=8192,s.from=function(t,e,r){return a(t,e,r)},Object.setPrototypeOf(s.prototype,Uint8Array.prototype),Object.setPrototypeOf(s,Uint8Array),s.alloc=function(t,e,r){return(u(t),t<=0)?f(t):void 0!==e?"string"==typeof r?f(t).fill(e,r):f(t).fill(e):f(t)},s.allocUnsafe=function(t){return l(t)},s.allocUnsafeSlow=function(t){return l(t)};function c(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function p(t,e){if(s.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||T(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var o=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return O(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return U(t).length;default:if(o)return n?-1:O(t).length;e=(""+e).toLowerCase(),o=!0}}function y(t,e,r){var o,i,f,s=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var o="",i=e;i<r;++i)o+=P[t[i]];return o}(this,e,r);case"utf8":case"utf-8":return m(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var o=e;o<r;++o)n+=String.fromCharCode(127&t[o]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var o=e;o<r;++o)n+=String.fromCharCode(t[o]);return n}(this,e,r);case"base64":return o=this,i=e,f=r,0===i&&f===o.length?n.fromByteArray(o):n.fromByteArray(o.slice(i,f));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),o="",i=0;i<n.length;i+=2)o+=String.fromCharCode(n[i]+256*n[i+1]);return o}(this,e,r);default:if(s)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),s=!0}}function d(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function g(t,e,r,n,o){var i;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(i=r*=1)!=i&&(r=o?0:t.length-1),r<0&&(r=t.length+r),r>=t.length)if(o)return -1;else r=t.length-1;else if(r<0)if(!o)return -1;else r=0;if("string"==typeof e&&(e=s.from(e,n)),s.isBuffer(e))return 0===e.length?-1:v(t,e,r,n,o);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(o)return Uint8Array.prototype.indexOf.call(t,e,r);else return Uint8Array.prototype.lastIndexOf.call(t,e,r);return v(t,[e],r,n,o)}throw TypeError("val must be string, number or Buffer")}function v(t,e,r,n,o){var i,f=1,s=t.length,a=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;f=2,s/=2,a/=2,r/=2}function u(t,e){return 1===f?t[e]:t.readUInt16BE(e*f)}if(o){var l=-1;for(i=r;i<s;i++)if(u(t,i)===u(e,-1===l?0:i-l)){if(-1===l&&(l=i),i-l+1===a)return l*f}else -1!==l&&(i-=i-l),l=-1}else for(r+a>s&&(r=s-a),i=r;i>=0;i--){for(var h=!0,c=0;c<a;c++)if(u(t,i+c)!==u(e,c)){h=!1;break}if(h)return i}return -1}s.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==s.prototype},s.compare=function(t,e){if(T(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),T(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),!s.isBuffer(t)||!s.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,o=0,i=Math.min(r,n);o<i;++o)if(t[o]!==e[o]){r=t[o],n=e[o];break}return r<n?-1:+(n<r)},s.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return s.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=s.allocUnsafe(e),o=0;for(r=0;r<t.length;++r){var i=t[r];if(T(i,Uint8Array)&&(i=s.from(i)),!s.isBuffer(i))throw TypeError('"list" argument must be an Array of Buffers');i.copy(n,o),o+=i.length}return n},s.byteLength=p,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)d(this,e,e+1);return this},s.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)d(this,e,e+3),d(this,e+1,e+2);return this},s.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)d(this,e,e+7),d(this,e+1,e+6),d(this,e+2,e+5),d(this,e+3,e+4);return this},s.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?m(this,0,t):y.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(t){if(!s.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===s.compare(this,t)},s.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},i&&(s.prototype[i]=s.prototype.inspect),s.prototype.compare=function(t,e,r,n,o){if(T(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),!s.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===o&&(o=this.length),e<0||r>t.length||n<0||o>this.length)throw RangeError("out of range index");if(n>=o&&e>=r)return 0;if(n>=o)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,o>>>=0,this===t)return 0;for(var i=o-n,f=r-e,a=Math.min(i,f),u=this.slice(n,o),l=t.slice(e,r),h=0;h<a;++h)if(u[h]!==l[h]){i=u[h],f=l[h];break}return i<f?-1:+(f<i)},s.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},s.prototype.indexOf=function(t,e,r){return g(this,t,e,r,!0)},s.prototype.lastIndexOf=function(t,e,r){return g(this,t,e,r,!1)};function m(t,e,r){r=Math.min(t.length,r);for(var n=[],o=e;o<r;){var i,f,s,a,u=t[o],l=null,h=u>239?4:u>223?3:u>191?2:1;if(o+h<=r)switch(h){case 1:u<128&&(l=u);break;case 2:(192&(i=t[o+1]))==128&&(a=(31&u)<<6|63&i)>127&&(l=a);break;case 3:i=t[o+1],f=t[o+2],(192&i)==128&&(192&f)==128&&(a=(15&u)<<12|(63&i)<<6|63&f)>2047&&(a<55296||a>57343)&&(l=a);break;case 4:i=t[o+1],f=t[o+2],s=t[o+3],(192&i)==128&&(192&f)==128&&(192&s)==128&&(a=(15&u)<<18|(63&i)<<12|(63&f)<<6|63&s)>65535&&a<1114112&&(l=a)}null===l?(l=65533,h=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),o+=h}var c=n,p=c.length;if(p<=4096)return String.fromCharCode.apply(String,c);for(var y="",d=0;d<p;)y+=String.fromCharCode.apply(String,c.slice(d,d+=4096));return y}function b(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function w(t,e,r,n,o,i){if(!s.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>o||e<i)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function E(t,e,r,n,o,i){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function x(t,e,r,n,i){return e*=1,r>>>=0,i||E(t,e,r,4,34028234663852886e22,-34028234663852886e22),o.write(t,e,r,n,23,4),r+4}function A(t,e,r,n,i){return e*=1,r>>>=0,i||E(t,e,r,8,17976931348623157e292,-17976931348623157e292),o.write(t,e,r,n,52,8),r+8}s.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var o,i,f,s,a,u,l,h,c=this.length-e;if((void 0===r||r>c)&&(r=c),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var p=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var o=t.length-r;n?(n=Number(n))>o&&(n=o):n=o;var i=e.length;n>i/2&&(n=i/2);for(var f=0;f<n;++f){var s,a=parseInt(e.substr(2*f,2),16);if((s=a)!=s)break;t[r+f]=a}return f}(this,t,e,r);case"utf8":case"utf-8":return o=e,i=r,R(O(t,this.length-o),this,o,i);case"ascii":return f=e,s=r,R(k(t),this,f,s);case"latin1":case"binary":return function(t,e,r,n){return R(k(e),t,r,n)}(this,t,e,r);case"base64":return a=e,u=r,R(U(t),this,a,u);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return l=e,h=r,R(function(t,e){for(var r,n,o=[],i=0;i<t.length&&!((e-=2)<0);++i)n=(r=t.charCodeAt(i))>>8,o.push(r%256),o.push(n);return o}(t,this.length-l),this,l,h);default:if(p)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},s.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,s.prototype),n},s.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],o=1,i=0;++i<e&&(o*=256);)n+=this[t+i]*o;return n},s.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t+--e],o=1;e>0&&(o*=256);)n+=this[t+--e]*o;return n},s.prototype.readUInt8=function(t,e){return t>>>=0,e||b(t,1,this.length),this[t]},s.prototype.readUInt16LE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]|this[t+1]<<8},s.prototype.readUInt16BE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]<<8|this[t+1]},s.prototype.readUInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},s.prototype.readUInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},s.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],o=1,i=0;++i<e&&(o*=256);)n+=this[t+i]*o;return n>=(o*=128)&&(n-=Math.pow(2,8*e)),n},s.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=e,o=1,i=this[t+--n];n>0&&(o*=256);)i+=this[t+--n]*o;return i>=(o*=128)&&(i-=Math.pow(2,8*e)),i},s.prototype.readInt8=function(t,e){return(t>>>=0,e||b(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},s.prototype.readInt16LE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?0xffff0000|r:r},s.prototype.readInt16BE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?0xffff0000|r:r},s.prototype.readInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},s.prototype.readInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},s.prototype.readFloatLE=function(t,e){return t>>>=0,e||b(t,4,this.length),o.read(this,t,!0,23,4)},s.prototype.readFloatBE=function(t,e){return t>>>=0,e||b(t,4,this.length),o.read(this,t,!1,23,4)},s.prototype.readDoubleLE=function(t,e){return t>>>=0,e||b(t,8,this.length),o.read(this,t,!0,52,8)},s.prototype.readDoubleBE=function(t,e){return t>>>=0,e||b(t,8,this.length),o.read(this,t,!1,52,8)},s.prototype.writeUIntLE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var o=Math.pow(2,8*r)-1;w(this,t,e,r,o,0)}var i=1,f=0;for(this[e]=255&t;++f<r&&(i*=256);)this[e+f]=t/i&255;return e+r},s.prototype.writeUIntBE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var o=Math.pow(2,8*r)-1;w(this,t,e,r,o,0)}var i=r-1,f=1;for(this[e+i]=255&t;--i>=0&&(f*=256);)this[e+i]=t/f&255;return e+r},s.prototype.writeUInt8=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,1,255,0),this[e]=255&t,e+1},s.prototype.writeUInt16LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},s.prototype.writeUInt16BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},s.prototype.writeUInt32LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},s.prototype.writeUInt32BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},s.prototype.writeIntLE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var o=Math.pow(2,8*r-1);w(this,t,e,r,o-1,-o)}var i=0,f=1,s=0;for(this[e]=255&t;++i<r&&(f*=256);)t<0&&0===s&&0!==this[e+i-1]&&(s=1),this[e+i]=(t/f|0)-s&255;return e+r},s.prototype.writeIntBE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var o=Math.pow(2,8*r-1);w(this,t,e,r,o-1,-o)}var i=r-1,f=1,s=0;for(this[e+i]=255&t;--i>=0&&(f*=256);)t<0&&0===s&&0!==this[e+i+1]&&(s=1),this[e+i]=(t/f|0)-s&255;return e+r},s.prototype.writeInt8=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},s.prototype.writeInt16LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},s.prototype.writeInt16BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},s.prototype.writeInt32LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},s.prototype.writeInt32BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},s.prototype.writeFloatLE=function(t,e,r){return x(this,t,e,!0,r)},s.prototype.writeFloatBE=function(t,e,r){return x(this,t,e,!1,r)},s.prototype.writeDoubleLE=function(t,e,r){return A(this,t,e,!0,r)},s.prototype.writeDoubleBE=function(t,e,r){return A(this,t,e,!1,r)},s.prototype.copy=function(t,e,r,n){if(!s.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var o=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var i=o-1;i>=0;--i)t[i+e]=this[i+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return o},s.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!s.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var o,i=t.charCodeAt(0);("utf8"===n&&i<128||"latin1"===n)&&(t=i)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(o=e;o<r;++o)this[o]=t;else{var f=s.isBuffer(t)?t:s.from(t,n),a=f.length;if(0===a)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(o=0;o<r-e;++o)this[o+e]=f[o%a]}return this};var B=/[^+/0-9A-Za-z-_]/g;function O(t,e){e=e||1/0;for(var r,n=t.length,o=null,i=[],f=0;f<n;++f){if((r=t.charCodeAt(f))>55295&&r<57344){if(!o){if(r>56319||f+1===n){(e-=3)>-1&&i.push(239,191,189);continue}o=r;continue}if(r<56320){(e-=3)>-1&&i.push(239,191,189),o=r;continue}r=(o-55296<<10|r-56320)+65536}else o&&(e-=3)>-1&&i.push(239,191,189);if(o=null,r<128){if((e-=1)<0)break;i.push(r)}else if(r<2048){if((e-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return i}function k(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}function U(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(B,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function R(t,e,r,n){for(var o=0;o<n&&!(o+r>=e.length)&&!(o>=t.length);++o)e[o+r]=t[o];return o}function T(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var P=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,o=0;o<16;++o)e[n+o]=t[r]+t[o];return e}()},783:function(t,e){e.read=function(t,e,r,n,o){var i,f,s=8*o-n-1,a=(1<<s)-1,u=a>>1,l=-7,h=r?o-1:0,c=r?-1:1,p=t[e+h];for(h+=c,i=p&(1<<-l)-1,p>>=-l,l+=s;l>0;i=256*i+t[e+h],h+=c,l-=8);for(f=i&(1<<-l)-1,i>>=-l,l+=n;l>0;f=256*f+t[e+h],h+=c,l-=8);if(0===i)i=1-u;else{if(i===a)return f?NaN:1/0*(p?-1:1);f+=Math.pow(2,n),i-=u}return(p?-1:1)*f*Math.pow(2,i-n)},e.write=function(t,e,r,n,o,i){var f,s,a,u=8*i-o-1,l=(1<<u)-1,h=l>>1,c=5960464477539062e-23*(23===o),p=n?0:i-1,y=n?1:-1,d=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(s=+!!isNaN(e),f=l):(f=Math.floor(Math.log(e)/Math.LN2),e*(a=Math.pow(2,-f))<1&&(f--,a*=2),f+h>=1?e+=c/a:e+=c*Math.pow(2,1-h),e*a>=2&&(f++,a/=2),f+h>=l?(s=0,f=l):f+h>=1?(s=(e*a-1)*Math.pow(2,o),f+=h):(s=e*Math.pow(2,h-1)*Math.pow(2,o),f=0));o>=8;t[r+p]=255&s,p+=y,s/=256,o-=8);for(f=f<<o|s,u+=o;u>0;t[r+p]=255&f,p+=y,f/=256,u-=8);t[r+p-y]|=128*d}}},r={};function n(t){var o=r[t];if(void 0!==o)return o.exports;var i=r[t]={exports:{}},f=!0;try{e[t](i,i.exports,n),f=!1}finally{f&&delete r[t]}return i.exports}n.ab="//",t.exports=n(72)}()}}]);