(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/EEGViewer/ChannelGrid.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_plotly_js_dist_plotly_26e3ab27.js",
  "static/chunks/node_modules_4a728ccf._.js",
  "static/chunks/src_components_EEGViewer_bc2a64f8._.js",
  "static/chunks/src_components_EEGViewer_ChannelGrid_tsx_0ae8890e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/EEGViewer/ChannelGrid.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);