{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/contexts/WebSocketContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useEffect, useState, useRef } from 'react';\r\n\r\ninterface ChunkResult {\r\n  chunk_number: number;\r\n  total_chunks: number;\r\n  time_range: [number, number];\r\n  hfo_events: unknown[];\r\n  channel_data: Record<string, number[]>;\r\n  progress: number;\r\n}\r\n\r\ninterface WebSocketContextType {\r\n  isConnected: boolean;\r\n  progress: number;\r\n  chunkResults: ChunkResult[];\r\n  error: string | null;\r\n}\r\n\r\nconst WebSocketContext = createContext<WebSocketContextType>({\r\n  isConnected: false,\r\n  progress: 0,\r\n  chunkResults: [],\r\n  error: null,\r\n});\r\n\r\nexport const useWebSocket = () => useContext(WebSocketContext);\r\n\r\nexport const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\r\n  const [isConnected, setIsConnected] = useState(false);\r\n  const [progress, setProgress] = useState(0);\r\n  const [chunkResults, setChunkResults] = useState<ChunkResult[]>([]);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const wsRef = useRef<WebSocket | null>(null);\r\n\r\n  useEffect(() => {\r\n    const ws = new WebSocket('ws://localhost:8000/ws');\r\n    wsRef.current = ws;\r\n\r\n    ws.onopen = () => {\r\n      console.log('WebSocket connected');\r\n      setIsConnected(true);\r\n      setError(null);\r\n    };\r\n\r\n    ws.onmessage = (event) => {\r\n      const message = JSON.parse(event.data);\r\n      console.log('WebSocket message:', message);\r\n\r\n      switch (message.type) {\r\n        case 'status':\r\n          console.log('Status:', message.message);\r\n          break;\r\n        \r\n        case 'preview':\r\n          console.log('Preview data received:', message.data);\r\n          break;\r\n        \r\n        case 'chunk':\r\n          setChunkResults(prev => [...prev, message.data]);\r\n          setProgress(message.data.progress);\r\n          break;\r\n        \r\n        case 'complete':\r\n          console.log('Analysis complete:', message.data);\r\n          setProgress(100);\r\n          break;\r\n        \r\n        case 'error':\r\n          setError(message.message);\r\n          console.error('WebSocket error:', message);\r\n          break;\r\n      }\r\n    };\r\n\r\n    ws.onerror = (error) => {\r\n      console.error('WebSocket error:', error);\r\n      setError('Connection error');\r\n      setIsConnected(false);\r\n    };\r\n\r\n    ws.onclose = () => {\r\n      console.log('WebSocket disconnected');\r\n      setIsConnected(false);\r\n    };\r\n\r\n    return () => {\r\n      if (ws.readyState === WebSocket.OPEN) {\r\n        ws.close();\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <WebSocketContext.Provider value={{ isConnected, progress, chunkResults, error }}>\r\n      {children}\r\n    </WebSocketContext.Provider>\r\n  );\r\n};"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAoBA,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAwB;IAC3D,aAAa;IACb,UAAU;IACV,cAAc,EAAE;IAChB,OAAO;AACT;AAEO,MAAM,eAAe,IAAM,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;AAEtC,MAAM,oBAA6D,CAAC,EAAE,QAAQ,EAAE;IACrF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,KAAK,IAAI,UAAU;QACzB,MAAM,OAAO,GAAG;QAEhB,GAAG,MAAM,GAAG;YACV,QAAQ,GAAG,CAAC;YACZ,eAAe;YACf,SAAS;QACX;QAEA,GAAG,SAAS,GAAG,CAAC;YACd,MAAM,UAAU,KAAK,KAAK,CAAC,MAAM,IAAI;YACrC,QAAQ,GAAG,CAAC,sBAAsB;YAElC,OAAQ,QAAQ,IAAI;gBAClB,KAAK;oBACH,QAAQ,GAAG,CAAC,WAAW,QAAQ,OAAO;oBACtC;gBAEF,KAAK;oBACH,QAAQ,GAAG,CAAC,0BAA0B,QAAQ,IAAI;oBAClD;gBAEF,KAAK;oBACH,gBAAgB,CAAA,OAAQ;+BAAI;4BAAM,QAAQ,IAAI;yBAAC;oBAC/C,YAAY,QAAQ,IAAI,CAAC,QAAQ;oBACjC;gBAEF,KAAK;oBACH,QAAQ,GAAG,CAAC,sBAAsB,QAAQ,IAAI;oBAC9C,YAAY;oBACZ;gBAEF,KAAK;oBACH,SAAS,QAAQ,OAAO;oBACxB,QAAQ,KAAK,CAAC,oBAAoB;oBAClC;YACJ;QACF;QAEA,GAAG,OAAO,GAAG,CAAC;YACZ,QAAQ,KAAK,CAAC,oBAAoB;YAClC,SAAS;YACT,eAAe;QACjB;QAEA,GAAG,OAAO,GAAG;YACX,QAAQ,GAAG,CAAC;YACZ,eAAe;QACjB;QAEA,OAAO;YACL,IAAI,GAAG,UAAU,KAAK,UAAU,IAAI,EAAE;gBACpC,GAAG,KAAK;YACV;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE;YAAa;YAAU;YAAc;QAAM;kBAC5E;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ConnectionStatus.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Wifi, WifiOff, Activity, AlertTriangle } from 'lucide-react';\r\nimport clsx from 'clsx';\r\n\r\ninterface ConnectionStatusProps {\r\n  isConnected: boolean;\r\n  progress?: number;\r\n  dataPoints?: number;\r\n  error?: string;\r\n  compact?: boolean;\r\n}\r\n\r\nconst ConnectionStatus: React.FC<ConnectionStatusProps> = ({\r\n  isConnected,\r\n  progress = 0,\r\n  dataPoints = 0,\r\n  error,\r\n  compact = false\r\n}) => {\r\n  if (compact) {\r\n    return (\r\n      <div className=\"flex items-center gap-2\">\r\n        <div className={clsx(\r\n          'status-dot',\r\n          isConnected ? 'connected' : 'disconnected'\r\n        )} />\r\n        <span className=\"text-sm font-medium text-gray-600\">\r\n          {isConnected ? 'Connected' : 'Disconnected'}\r\n        </span>\r\n        {progress > 0 && progress < 100 && (\r\n          <span className=\"text-sm text-gray-500\">\r\n            • {Math.round(progress)}%\r\n          </span>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"glass-card p-4 animate-fadeIn\">\r\n      <div className=\"flex items-start justify-between\">\r\n        <div className=\"flex items-start gap-3\">\r\n          <div className=\"p-2 rounded bg-gray-50\">\r\n            {isConnected ? (\r\n              <Wifi className=\"w-5 h-5 text-black\" />\r\n            ) : error ? (\r\n              <AlertTriangle className=\"w-5 h-5 text-red-600\" />\r\n            ) : (\r\n              <WifiOff className=\"w-5 h-5 text-gray-400\" />\r\n            )}\r\n          </div>\r\n          \r\n          <div className=\"flex-1\">\r\n            <div className=\"flex items-center gap-2 mb-1\">\r\n              <h3 className=\"text-sm font-semibold text-gray-900\">\r\n                Connection Status\r\n              </h3>\r\n              <div className={clsx(\r\n                'status-dot',\r\n                isConnected ? 'connected' : 'disconnected'\r\n              )} />\r\n            </div>\r\n            \r\n            <p className={clsx(\r\n              'text-sm',\r\n              isConnected ? 'text-gray-700' : error ? 'text-red-600' : 'text-gray-500'\r\n            )}>\r\n              {isConnected \r\n                ? 'WebSocket connected' \r\n                : error \r\n                  ? 'Connection error'\r\n                  : 'Waiting for connection...'}\r\n            </p>\r\n            \r\n            {error && (\r\n              <p className=\"text-xs text-red-500 mt-1\">\r\n                {error}\r\n              </p>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {isConnected && dataPoints > 0 && (\r\n          <div className=\"text-right\">\r\n            <div className=\"flex items-center gap-1 text-gray-700\">\r\n              <Activity className=\"w-4 h-4\" />\r\n              <span className=\"text-sm font-semibold\">\r\n                {dataPoints.toLocaleString()}\r\n              </span>\r\n            </div>\r\n            <span className=\"text-xs text-gray-500\">\r\n              data points\r\n            </span>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {isConnected && progress > 0 && progress < 100 && (\r\n        <div className=\"mt-3 pt-3 border-t border-gray-100\">\r\n          <div className=\"flex items-center justify-between mb-1\">\r\n            <span className=\"text-xs font-medium text-gray-600\">\r\n              Processing\r\n            </span>\r\n            <span className=\"text-xs font-semibold text-gray-700\">\r\n              {Math.round(progress)}%\r\n            </span>\r\n          </div>\r\n          <div className=\"progress-bar\">\r\n            <div \r\n              className=\"progress-fill\"\r\n              style={{ width: `${progress}%` }}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ConnectionStatus;"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AAJA;;;;AAcA,MAAM,mBAAoD,CAAC,EACzD,WAAW,EACX,WAAW,CAAC,EACZ,aAAa,CAAC,EACd,KAAK,EACL,UAAU,KAAK,EAChB;IACC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EACjB,cACA,cAAc,cAAc;;;;;;8BAE9B,8OAAC;oBAAK,WAAU;8BACb,cAAc,cAAc;;;;;;gBAE9B,WAAW,KAAK,WAAW,qBAC1B,8OAAC;oBAAK,WAAU;;wBAAwB;wBACnC,KAAK,KAAK,CAAC;wBAAU;;;;;;;;;;;;;IAKlC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,4BACC,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;+EACd,sBACF,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;6FAEzB,8OAAC,4MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;0CAIvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DAGpD,8OAAC;gDAAI,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EACjB,cACA,cAAc,cAAc;;;;;;;;;;;;kDAIhC,8OAAC;wCAAE,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EACf,WACA,cAAc,kBAAkB,QAAQ,iBAAiB;kDAExD,cACG,wBACA,QACE,qBACA;;;;;;oCAGP,uBACC,8OAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;oBAMR,eAAe,aAAa,mBAC3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDACb,WAAW,cAAc;;;;;;;;;;;;0CAG9B,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAO7C,eAAe,WAAW,KAAK,WAAW,qBACzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAoC;;;;;;0CAGpD,8OAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC;oCAAU;;;;;;;;;;;;;kCAG1B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,SAAS,CAAC,CAAC;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAO7C;uCAEe", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ProgressIndicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Activity, CheckCircle } from 'lucide-react';\r\nimport clsx from 'clsx';\r\n\r\ninterface ProgressIndicatorProps {\r\n  progress: number;\r\n  label?: string;\r\n  showPercentage?: boolean;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  variant?: 'default' | 'gradient' | 'medical';\r\n}\r\n\r\nconst ProgressIndicator: React.FC<ProgressIndicatorProps> = ({\r\n  progress,\r\n  label = 'Processing',\r\n  showPercentage = true,\r\n  size = 'md',\r\n}) => {\r\n  const isComplete = progress >= 100;\r\n  \r\n  const sizeClasses = {\r\n    sm: 'h-1',\r\n    md: 'h-1.5',\r\n    lg: 'h-2'\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {(label || showPercentage) && (\r\n        <div className=\"flex items-center justify-between mb-2\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {isComplete ? (\r\n              <CheckCircle className=\"w-4 h-4 text-black\" />\r\n            ) : (\r\n              <Activity className=\"w-4 h-4 text-gray-600 pulse\" />\r\n            )}\r\n            <span className=\"text-sm font-medium text-gray-700\">\r\n              {label}\r\n            </span>\r\n          </div>\r\n          {showPercentage && (\r\n            <span className={clsx(\r\n              'text-sm font-semibold',\r\n              isComplete ? 'text-black' : 'text-gray-600'\r\n            )}>\r\n              {Math.round(progress)}%\r\n            </span>\r\n          )}\r\n        </div>\r\n      )}\r\n      \r\n      <div className={clsx(\r\n        'w-full bg-gray-200 rounded overflow-hidden',\r\n        sizeClasses[size]\r\n      )}>\r\n        <div\r\n          className=\"h-full bg-black rounded transition-all duration-500 ease-out\"\r\n          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}\r\n        />\r\n      </div>\r\n\r\n      {isComplete && (\r\n        <div className=\"mt-2 flex items-center gap-2 animate-slideIn\">\r\n          <CheckCircle className=\"w-3.5 h-3.5 text-black\" />\r\n          <span className=\"text-xs text-black font-medium\">\r\n            Complete\r\n          </span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProgressIndicator;"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAJA;;;;AAcA,MAAM,oBAAsD,CAAC,EAC3D,QAAQ,EACR,QAAQ,YAAY,EACpB,iBAAiB,IAAI,EACrB,OAAO,IAAI,EACZ;IACC,MAAM,aAAa,YAAY;IAE/B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,CAAC,SAAS,cAAc,mBACvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ,2BACC,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;yFAEvB,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CAEtB,8OAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;oBAGJ,gCACC,8OAAC;wBAAK,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAClB,yBACA,aAAa,eAAe;;4BAE3B,KAAK,KAAK,CAAC;4BAAU;;;;;;;;;;;;;0BAM9B,8OAAC;gBAAI,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EACjB,8CACA,WAAW,CAAC,KAAK;0BAEjB,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;YAI9D,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAK,WAAU;kCAAiC;;;;;;;;;;;;;;;;;;AAO3D;uCAEe", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/EEGViewer/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport dynamic from 'next/dynamic';\r\nimport { useWebSocket } from '@/contexts/WebSocketContext';\r\nimport ConnectionStatus from '@/components/ConnectionStatus';\r\nimport ProgressIndicator from '@/components/ProgressIndicator';\r\nimport { Layers, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, Download } from 'lucide-react';\r\nimport clsx from 'clsx';\r\n\r\nconst ChannelGrid = dynamic(() => import('./ChannelGrid'), { \r\n  ssr: false,\r\n  loading: () => (\r\n    <div className=\"flex items-center justify-center h-96\">\r\n      <div className=\"text-center\">\r\n        <div className=\"animate-pulse\">\r\n          <Layers className=\"w-12 h-12 text-gray-400 mx-auto mb-3\" />\r\n        </div>\r\n        <p className=\"text-sm text-gray-600\">Loading visualization...</p>\r\n      </div>\r\n    </div>\r\n  )\r\n});\r\n\r\nconst EEGViewer: React.FC = () => {\r\n  const { isConnected, progress, chunkResults, error } = useWebSocket();\r\n  const [timeWindow, setTimeWindow] = useState<[number, number]>([0, 10]);\r\n  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);\r\n\r\n  const allChannelData: Record<string, number[]> = {};\r\n  \r\n  chunkResults.forEach((chunk) => {\r\n    if (chunk.channel_data) {\r\n      Object.entries(chunk.channel_data).forEach(([channel, data]) => {\r\n        if (!allChannelData[channel]) {\r\n          allChannelData[channel] = [];\r\n        }\r\n        allChannelData[channel].push(...data);\r\n      });\r\n    }\r\n  });\r\n\r\n  const channels = Object.keys(allChannelData);\r\n  const visibleChannels = selectedChannels.length > 0 ? selectedChannels : channels;\r\n  \r\n  const totalDataPoints = Object.values(allChannelData).reduce(\r\n    (sum, data) => sum + data.length, 0\r\n  );\r\n\r\n  useEffect(() => {\r\n    if (channels.length > 0 && selectedChannels.length === 0) {\r\n      setSelectedChannels(channels.slice(0, Math.min(10, channels.length)));\r\n    }\r\n  }, [channels.length]);\r\n\r\n  const handleTimeNavigation = (direction: 'prev' | 'next') => {\r\n    const shift = direction === 'next' ? 10 : -10;\r\n    setTimeWindow(([start, end]) => [\r\n      Math.max(0, start + shift),\r\n      Math.max(10, end + shift)\r\n    ]);\r\n  };\r\n\r\n  const handleZoom = (type: 'in' | 'out') => {\r\n    const factor = type === 'in' ? 0.5 : 2;\r\n    setTimeWindow(([start, end]) => {\r\n      const center = (start + end) / 2;\r\n      const halfRange = ((end - start) / 2) * factor;\r\n      return [\r\n        Math.max(0, center - halfRange),\r\n        center + halfRange\r\n      ];\r\n    });\r\n  };\r\n\r\n  const toggleChannel = (channel: string) => {\r\n    setSelectedChannels(prev => \r\n      prev.includes(channel)\r\n        ? prev.filter(ch => ch !== channel)\r\n        : [...prev, channel]\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-screen w-screen flex flex-col bg-white\">\r\n      <header className=\"flex-shrink-0 h-16 bg-white border-b border-gray-200 px-4 flex items-center justify-between\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div>\r\n            <h1 className=\"text-lg font-bold text-black\">Biormika HFO Detector</h1>\r\n            <p className=\"text-xs text-gray-600\">Real-time High-Frequency Oscillation Detection</p>\r\n          </div>\r\n          <ConnectionStatus\r\n            isConnected={isConnected}\r\n            progress={progress}\r\n            dataPoints={totalDataPoints}\r\n            error={error}\r\n          />\r\n        </div>\r\n        <div className=\"flex items-center gap-4\">\r\n          <ProgressIndicator\r\n            progress={progress}\r\n            label=\"Analysis Progress\"\r\n            variant=\"medical\"\r\n            size=\"sm\"\r\n          />\r\n        </div>\r\n      </header>\r\n\r\n      {channels.length > 0 ? (\r\n        <div className=\"flex-1 flex overflow-hidden\">\r\n          <div className=\"flex-1 flex flex-col\">\r\n            <div className=\"flex-shrink-0 h-12 bg-gray-50 border-b border-gray-200 px-4 flex items-center justify-between\">\r\n              <div className=\"flex items-center gap-4\">\r\n                <h2 className=\"text-sm font-semibold text-gray-900\">\r\n                  EEG Signal Analysis\r\n                </h2>\r\n                <span className=\"text-xs text-gray-600\">\r\n                  Showing {visibleChannels.length} of {channels.length} channels\r\n                </span>\r\n              </div>\r\n                  \r\n              <div className=\"flex items-center gap-2\">\r\n                <div className=\"flex items-center gap-1 bg-white rounded p-1\">\r\n                  <button\r\n                    onClick={() => handleTimeNavigation('prev')}\r\n                    className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\r\n                    title=\"Previous 10 seconds\"\r\n                  >\r\n                    <ChevronLeft className=\"w-3 h-3 text-gray-600\" />\r\n                  </button>\r\n                  <span className=\"px-2 text-xs font-medium text-gray-700\">\r\n                    {timeWindow[0]}s - {timeWindow[1]}s\r\n                  </span>\r\n                  <button\r\n                    onClick={() => handleTimeNavigation('next')}\r\n                    className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\r\n                    title=\"Next 10 seconds\"\r\n                  >\r\n                    <ChevronRight className=\"w-3 h-3 text-gray-600\" />\r\n                  </button>\r\n                </div>\r\n                \r\n                <div className=\"flex items-center gap-1 bg-white rounded p-1\">\r\n                  <button\r\n                    onClick={() => handleZoom('in')}\r\n                    className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\r\n                    title=\"Zoom in\"\r\n                  >\r\n                    <ZoomIn className=\"w-3 h-3 text-gray-600\" />\r\n                  </button>\r\n                  <button\r\n                    onClick={() => handleZoom('out')}\r\n                    className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\r\n                    title=\"Zoom out\"\r\n                  >\r\n                    <ZoomOut className=\"w-3 h-3 text-gray-600\" />\r\n                  </button>\r\n                </div>\r\n                \r\n                <button className=\"p-1 hover:bg-gray-100 rounded transition-colors\">\r\n                  <Download className=\"w-3 h-3 text-gray-600\" />\r\n                </button>\r\n              </div>\r\n            </div>\r\n              \r\n            <ChannelGrid\r\n              channelData={allChannelData}\r\n              visibleChannels={visibleChannels}\r\n              timeWindow={timeWindow}\r\n              samplingRate={256}\r\n              showHFOMarkers={false}\r\n              hfoEvents={[]}\r\n              channelHeight={80}\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"flex-shrink-0 w-64 bg-gray-50 border-l border-gray-200 overflow-hidden flex flex-col\">\r\n            <div className=\"p-3 border-b border-gray-200\">\r\n              <h3 className=\"text-xs font-semibold text-gray-900 flex items-center gap-2\">\r\n                <Layers className=\"w-3 h-3 text-gray-600\" />\r\n                Channel Selection\r\n              </h3>\r\n            </div>\r\n              \r\n            <div className=\"flex-1 overflow-y-auto p-3\">\r\n              <div className=\"space-y-1\">\r\n                  {channels.map((channel, index) => (\r\n                    <label\r\n                      key={channel}\r\n                      className={clsx(\r\n                        'flex items-center gap-2 p-1.5 rounded cursor-pointer transition-all',\r\n                        'hover:bg-white',\r\n                        selectedChannels.includes(channel) && 'bg-white border border-gray-300'\r\n                      )}\r\n                    >\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={selectedChannels.includes(channel)}\r\n                        onChange={() => toggleChannel(channel)}\r\n                        className=\"w-3 h-3 text-gray-700 border-gray-300 rounded focus:ring-gray-500\"\r\n                      />\r\n                      <div className=\"flex-1\">\r\n                        <span className=\"text-xs font-medium text-gray-700\">\r\n                          {channel}\r\n                        </span>\r\n                        <div\r\n                          className=\"h-1 mt-1 rounded-full\"\r\n                          style={{\r\n                            background: selectedChannels.includes(channel) ? '#000000' : '#d1d5db'\r\n                          }}\r\n                        />\r\n                      </div>\r\n                    </label>\r\n                  ))}\r\n                </div>\r\n                \r\n              <div className=\"mt-3 pt-3 border-t border-gray-200\">\r\n                  <button\r\n                    onClick={() => setSelectedChannels(channels)}\r\n                    className=\"w-full text-xs text-gray-700 hover:text-black font-medium\"\r\n                  >\r\n                    Select All\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setSelectedChannels([])}\r\n                    className=\"w-full text-xs text-gray-600 hover:text-gray-700 font-medium mt-1\"\r\n                  >\r\n                    Clear Selection\r\n                  </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        <div className=\"flex-1 flex items-center justify-center\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-pulse mb-4\">\r\n              <Layers className=\"w-16 h-16 text-gray-400 mx-auto\" />\r\n            </div>\r\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\r\n              {isConnected ? 'Waiting for EEG Data' : 'Connecting to Server'}\r\n            </h3>\r\n            <p className=\"text-sm text-gray-600\">\r\n              {isConnected \r\n                ? 'The analysis will begin shortly. Please wait...'\r\n                : 'Establishing WebSocket connection...'}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EEGViewer;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;AARA;;;;;;;;;AAUA,MAAM,cAAc,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IACxB,KAAK;IACL,SAAS,kBACP,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAM7C,MAAM,YAAsB;IAC1B,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QAAC;QAAG;KAAG;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErE,MAAM,iBAA2C,CAAC;IAElD,aAAa,OAAO,CAAC,CAAC;QACpB,IAAI,MAAM,YAAY,EAAE;YACtB,OAAO,OAAO,CAAC,MAAM,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,KAAK;gBACzD,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;oBAC5B,cAAc,CAAC,QAAQ,GAAG,EAAE;gBAC9B;gBACA,cAAc,CAAC,QAAQ,CAAC,IAAI,IAAI;YAClC;QACF;IACF;IAEA,MAAM,WAAW,OAAO,IAAI,CAAC;IAC7B,MAAM,kBAAkB,iBAAiB,MAAM,GAAG,IAAI,mBAAmB;IAEzE,MAAM,kBAAkB,OAAO,MAAM,CAAC,gBAAgB,MAAM,CAC1D,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE;IAGpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,GAAG,KAAK,iBAAiB,MAAM,KAAK,GAAG;YACxD,oBAAoB,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,SAAS,MAAM;QACpE;IACF,GAAG;QAAC,SAAS,MAAM;KAAC;IAEpB,MAAM,uBAAuB,CAAC;QAC5B,MAAM,QAAQ,cAAc,SAAS,KAAK,CAAC;QAC3C,cAAc,CAAC,CAAC,OAAO,IAAI,GAAK;gBAC9B,KAAK,GAAG,CAAC,GAAG,QAAQ;gBACpB,KAAK,GAAG,CAAC,IAAI,MAAM;aACpB;IACH;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,SAAS,SAAS,OAAO,MAAM;QACrC,cAAc,CAAC,CAAC,OAAO,IAAI;YACzB,MAAM,SAAS,CAAC,QAAQ,GAAG,IAAI;YAC/B,MAAM,YAAY,AAAC,CAAC,MAAM,KAAK,IAAI,IAAK;YACxC,OAAO;gBACL,KAAK,GAAG,CAAC,GAAG,SAAS;gBACrB,SAAS;aACV;QACH;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,CAAA,OAClB,KAAK,QAAQ,CAAC,WACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,WACzB;mBAAI;gBAAM;aAAQ;IAE1B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,8OAAC,sIAAA,CAAA,UAAgB;gCACf,aAAa;gCACb,UAAU;gCACV,YAAY;gCACZ,OAAO;;;;;;;;;;;;kCAGX,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uIAAA,CAAA,UAAiB;4BAChB,UAAU;4BACV,OAAM;4BACN,SAAQ;4BACR,MAAK;;;;;;;;;;;;;;;;;YAKV,SAAS,MAAM,GAAG,kBACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DAGpD,8OAAC;gDAAK,WAAU;;oDAAwB;oDAC7B,gBAAgB,MAAM;oDAAC;oDAAK,SAAS,MAAM;oDAAC;;;;;;;;;;;;;kDAIzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,qBAAqB;wDACpC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,8OAAC;wDAAK,WAAU;;4DACb,UAAU,CAAC,EAAE;4DAAC;4DAAK,UAAU,CAAC,EAAE;4DAAC;;;;;;;kEAEpC,8OAAC;wDACC,SAAS,IAAM,qBAAqB;wDACpC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAI5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,WAAW;wDAC1B,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,8OAAC;wDACC,SAAS,IAAM,WAAW;wDAC1B,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,4MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIvB,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAK1B,8OAAC;gCACC,aAAa;gCACb,iBAAiB;gCACjB,YAAY;gCACZ,cAAc;gCACd,gBAAgB;gCAChB,WAAW,EAAE;gCACb,eAAe;;;;;;;;;;;;kCAInB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;;;;;;0CAKhD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACV,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gDAEC,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EACZ,uEACA,kBACA,iBAAiB,QAAQ,CAAC,YAAY;;kEAGxC,8OAAC;wDACC,MAAK;wDACL,SAAS,iBAAiB,QAAQ,CAAC;wDACnC,UAAU,IAAM,cAAc;wDAC9B,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb;;;;;;0EAEH,8OAAC;gEACC,WAAU;gEACV,OAAO;oEACL,YAAY,iBAAiB,QAAQ,CAAC,WAAW,YAAY;gEAC/D;;;;;;;;;;;;;+CArBC;;;;;;;;;;kDA4Bb,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDACC,SAAS,IAAM,oBAAoB;gDACnC,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,oBAAoB,EAAE;gDACrC,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yEAQX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,8OAAC;4BAAG,WAAU;sCACX,cAAc,yBAAyB;;;;;;sCAE1C,8OAAC;4BAAE,WAAU;sCACV,cACG,oDACA;;;;;;;;;;;;;;;;;;;;;;;AAOlB;uCAEe", "debugId": null}}, {"offset": {"line": 1054, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/FileUploadCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, DragEvent } from \"react\";\r\nimport { Upload, FileText, X, AlertCircle } from \"lucide-react\";\r\nimport clsx from \"clsx\";\r\n\r\ninterface FileUploadCardProps {\r\n  onFileSelect: (filepath: string) => void;\r\n  onStartAnalysis: () => void;\r\n  onFileValidated?: (filepath: string) => Promise<void>;\r\n  error?: string;\r\n}\r\n\r\nconst FileUploadCard: React.FC<FileUploadCardProps> = ({ onFileSelect, onStartAnalysis, onFileValidated, error }) => {\r\n  const [filepath, setFilepath] = useState(\"\");\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [isValidating, setIsValidating] = useState(false);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(true);\r\n  };\r\n\r\n  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n  };\r\n\r\n  const handleDrop = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n\r\n    const files = e.dataTransfer.files;\r\n    if (files.length > 0) {\r\n      const file = files[0];\r\n      if (file.name.endsWith(\".edf\")) {\r\n        setFilepath(file.name);\r\n        onFileSelect(file.name);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (value: string) => {\r\n    setFilepath(value);\r\n    onFileSelect(value);\r\n  };\r\n\r\n  const clearInput = () => {\r\n    setFilepath(\"\");\r\n    onFileSelect(\"\");\r\n  };\r\n\r\n  return (\r\n    <div className=\"card-soft\">\r\n      <div className=\"p-6\">\r\n        <div className=\"mt-4\">\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">File path</label>\r\n          <div className=\"relative\">\r\n            <input\r\n              type=\"text\"\r\n              value={filepath}\r\n              onChange={(e) => handleInputChange(e.target.value)}\r\n              placeholder=\"/path/to/file.edf\"\r\n              className=\"input-modern\"\r\n            />\r\n            {filepath && (\r\n              <button onClick={clearInput} className=\"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600\">\r\n                <X className=\"w-4 h-4\" />\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {error && (\r\n          <div className=\"mt-4 p-3 rounded bg-red-50 border border-red-200 flex items-start gap-2\">\r\n            <AlertCircle className=\"w-4 h-4 text-red-500 flex-shrink-0 mt-0.5\" />\r\n            <p className=\"text-sm text-red-700\">{error}</p>\r\n          </div>\r\n        )}\r\n\r\n        {filepath && (\r\n          <div className=\"mt-3 p-2 rounded bg-gray-50 border border-gray-200 flex items-center gap-2\">\r\n            <FileText className=\"w-4 h-4 text-gray-600\" />\r\n            <span className=\"text-sm text-gray-700 truncate\">{filepath}</span>\r\n          </div>\r\n        )}\r\n\r\n        <button\r\n          onClick={async () => {\r\n            if (onFileValidated && filepath) {\r\n              setIsValidating(true);\r\n              try {\r\n                await onFileValidated(filepath);\r\n              } catch (error) {\r\n                // Error will be handled by parent component\r\n              } finally {\r\n                setIsValidating(false);\r\n              }\r\n            } else {\r\n              onStartAnalysis();\r\n            }\r\n          }}\r\n          disabled={!filepath || isValidating}\r\n          className={clsx(\"gradient-button w-full mt-4\", (!filepath || isValidating) && \"opacity-50 cursor-not-allowed\")}\r\n        >\r\n          {isValidating ? (\r\n            <div className=\"flex items-center justify-center gap-2\">\r\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\r\n              Validating File...\r\n            </div>\r\n          ) : onFileValidated ? (\r\n            \"Load File & Configure Parameters\"\r\n          ) : (\r\n            \"Start Analysis\"\r\n          )}\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileUploadCard;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAaA,MAAM,iBAAgD,CAAC,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,KAAK,EAAE;IAC9G,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;gBAC9B,YAAY,KAAK,IAAI;gBACrB,aAAa,KAAK,IAAI;YACxB;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;QACZ,aAAa;IACf;IAEA,MAAM,aAAa;QACjB,YAAY;QACZ,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;sCAA+C;;;;;;sCAChE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACjD,aAAY;oCACZ,WAAU;;;;;;gCAEX,0BACC,8OAAC;oCAAO,SAAS;oCAAY,WAAU;8CACrC,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAMpB,uBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;gBAIxC,0BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAK,WAAU;sCAAkC;;;;;;;;;;;;8BAItD,8OAAC;oBACC,SAAS;wBACP,IAAI,mBAAmB,UAAU;4BAC/B,gBAAgB;4BAChB,IAAI;gCACF,MAAM,gBAAgB;4BACxB,EAAE,OAAO,OAAO;4BACd,4CAA4C;4BAC9C,SAAU;gCACR,gBAAgB;4BAClB;wBACF,OAAO;4BACL;wBACF;oBACF;oBACA,UAAU,CAAC,YAAY;oBACvB,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,+BAA+B,CAAC,CAAC,YAAY,YAAY,KAAK;8BAE7E,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;4BAAkE;;;;;;mEAGjF,kBACF,qCAEA;;;;;;;;;;;;;;;;;AAMZ;uCAEe", "debugId": null}}, {"offset": {"line": 1262, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/types/eeg.ts"], "sourcesContent": ["export interface ThresholdParameters {\r\n  amplitude1: number;\r\n  amplitude2: number;\r\n  peaks1: number;\r\n  peaks2: number;\r\n  duration: number;\r\n  temporal_sync: number;\r\n  spatial_sync: number;\r\n}\r\n\r\nexport interface ThresholdParameterOptions {\r\n  amplitude1: { min: number; max: number; default: number; description: string };\r\n  amplitude2: { min: number; max: number; default: number; description: string };\r\n  peaks1: { min: number; max: number; default: number; description: string };\r\n  peaks2: { min: number; max: number; default: number; description: string };\r\n  duration: { min: number; max: number; default: number; description: string };\r\n  temporal_sync: { min: number; max: number; default: number; description: string };\r\n  spatial_sync: { min: number; max: number; default: number; description: string };\r\n}\r\n\r\nexport interface MontageConfig {\r\n  type: 'bipolar' | 'average' | 'referential';\r\n  reference_channel?: string;\r\n}\r\n\r\nexport interface FrequencyFilter {\r\n  low_cutoff: number;\r\n  high_cutoff: number;\r\n}\r\n\r\nexport interface FrequencyFilterOptions {\r\n  low_cutoff_options: number[];\r\n  high_cutoff_options: number[];\r\n  default_low: number;\r\n  default_high: number;\r\n}\r\n\r\nexport interface TimeSegment {\r\n  mode: 'entire_file' | 'start_end_times' | 'start_time_duration';\r\n  start_date?: string;\r\n  start_time?: string;\r\n  end_date?: string;\r\n  end_time?: string;\r\n  duration_seconds?: number;\r\n}\r\n\r\nexport interface ChannelSelection {\r\n  selected_leads: string[];\r\n  contact_specifications: Record<string, string>;\r\n}\r\n\r\nexport interface AnalysisParameters {\r\n  thresholds: ThresholdParameters;\r\n  montage: MontageConfig;\r\n  frequency: FrequencyFilter;\r\n  time_segment: TimeSegment;\r\n  channel_selection: ChannelSelection;\r\n}\r\n\r\nexport interface FileInfo {\r\n  file_id?: string;\r\n  filename: string;\r\n  filepath: string;\r\n  start_date: string;\r\n  start_time: string;\r\n  end_date: string;\r\n  end_time: string;\r\n  sampling_rate: number;\r\n  max_frequency: number;\r\n  channels: string[];\r\n  channel_groups: Record<string, number[]>;\r\n  duration_seconds: number;\r\n}\r\n\r\nexport interface JobStatus {\r\n  job_id: string;\r\n  file_id: string;\r\n  status: 'queued' | 'processing' | 'completed' | 'error';\r\n  progress: number;\r\n  error?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface HFOEvent {\r\n  channel: string;\r\n  start_time: number;\r\n  end_time: number;\r\n  amplitude: number;\r\n  frequency: number;\r\n}\r\n\r\nexport interface ChunkResult {\r\n  type: string;\r\n  time_range: [number, number];\r\n  hfo_events: HFOEvent[];\r\n  channel_data: Record<string, number[]>;\r\n  progress: number;\r\n  chunk_number: number;\r\n  total_chunks: number;\r\n}\r\n\r\nexport interface WebSocketMessage {\r\n  type: 'preview_ready' | 'chunk_complete' | 'hfo_detected' | 'analysis_complete' | 'error' | 'status_update' | 'validation_error' | 'validation_warning';\r\n  data?: Record<string, unknown>;\r\n  message?: string;\r\n  status?: string;\r\n  progress?: number;\r\n  errors?: ValidationError[];\r\n  warnings?: string[];\r\n}\r\n\r\nexport interface ValidationError {\r\n  field: string;\r\n  message: string;\r\n  value?: unknown;\r\n}\r\n\r\nexport interface ParameterOptions {\r\n  thresholds: ThresholdParameterOptions;\r\n  montage: {\r\n    types: string[];\r\n    default: string;\r\n  };\r\n  frequency: FrequencyFilterOptions;\r\n  time_segment: {\r\n    modes: string[];\r\n    default: string;\r\n  };\r\n}\r\n\r\nexport interface AnalysisState {\r\n  step: 'file_selection' | 'parameter_configuration' | 'analysis' | 'results';\r\n  fileInfo?: FileInfo;\r\n  parameters: AnalysisParameters;\r\n  validationErrors: ValidationError[];\r\n  isValidating: boolean;\r\n  canStartAnalysis: boolean;\r\n}\r\n\r\nexport interface SettingsData {\r\n  parameters: AnalysisParameters;\r\n  timestamp: string;\r\n  filename: string;\r\n}\r\n\r\nexport const DEFAULT_PARAMETERS: AnalysisParameters = {\r\n  thresholds: {\r\n    amplitude1: 2,\r\n    amplitude2: 2,\r\n    peaks1: 6,\r\n    peaks2: 3,\r\n    duration: 10,\r\n    temporal_sync: 10,\r\n    spatial_sync: 10\r\n  },\r\n  montage: {\r\n    type: 'bipolar'\r\n  },\r\n  frequency: {\r\n    low_cutoff: 50,\r\n    high_cutoff: 300\r\n  },\r\n  time_segment: {\r\n    mode: 'entire_file'\r\n  },\r\n  channel_selection: {\r\n    selected_leads: [],\r\n    contact_specifications: {}\r\n  }\r\n};"], "names": [], "mappings": ";;;AAkJO,MAAM,qBAAyC;IACpD,YAAY;QACV,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,eAAe;QACf,cAAc;IAChB;IACA,SAAS;QACP,MAAM;IACR;IACA,WAAW;QACT,YAAY;QACZ,aAAa;IACf;IACA,cAAc;QACZ,MAAM;IACR;IACA,mBAAmB;QACjB,gBAAgB,EAAE;QAClB,wBAAwB,CAAC;IAC3B;AACF", "debugId": null}}, {"offset": {"line": 1294, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/contexts/ParameterContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';\r\nimport { \r\n  AnalysisState, \r\n  AnalysisParameters, \r\n  FileInfo, \r\n  ValidationError, \r\n  ParameterOptions,\r\n  DEFAULT_PARAMETERS \r\n} from '@/types/eeg';\r\n\r\ninterface ParameterContextType {\r\n  state: AnalysisState;\r\n  updateFileInfo: (fileInfo: FileInfo) => void;\r\n  updateParameters: (parameters: Partial<AnalysisParameters>) => void;\r\n  updateThresholds: (thresholds: Partial<AnalysisParameters['thresholds']>) => void;\r\n  updateMontage: (montage: Partial<AnalysisParameters['montage']>) => void;\r\n  updateFrequency: (frequency: Partial<AnalysisParameters['frequency']>) => void;\r\n  updateTimeSegment: (timeSegment: Partial<AnalysisParameters['time_segment']>) => void;\r\n  updateChannelSelection: (channelSelection: Partial<AnalysisParameters['channel_selection']>) => void;\r\n  validateParameters: () => Promise<boolean>;\r\n  resetParameters: () => void;\r\n  loadParameters: (parameters: AnalysisParameters) => void;\r\n  setStep: (step: AnalysisState['step']) => void;\r\n  parameterOptions?: ParameterOptions;\r\n}\r\n\r\ntype ParameterAction = \r\n  | { type: 'SET_FILE_INFO'; payload: FileInfo }\r\n  | { type: 'UPDATE_PARAMETERS'; payload: Partial<AnalysisParameters> }\r\n  | { type: 'SET_VALIDATION_ERRORS'; payload: ValidationError[] }\r\n  | { type: 'SET_VALIDATING'; payload: boolean }\r\n  | { type: 'SET_CAN_START_ANALYSIS'; payload: boolean }\r\n  | { type: 'RESET_PARAMETERS' }\r\n  | { type: 'SET_STEP'; payload: AnalysisState['step'] }\r\n  | { type: 'SET_PARAMETER_OPTIONS'; payload: ParameterOptions };\r\n\r\nconst initialState: AnalysisState = {\r\n  step: 'file_selection',\r\n  parameters: DEFAULT_PARAMETERS,\r\n  validationErrors: [],\r\n  isValidating: false,\r\n  canStartAnalysis: false,\r\n};\r\n\r\nfunction parameterReducer(state: AnalysisState, action: ParameterAction): AnalysisState {\r\n  switch (action.type) {\r\n    case 'SET_FILE_INFO':\r\n      return {\r\n        ...state,\r\n        fileInfo: action.payload,\r\n        step: 'parameter_configuration',\r\n      };\r\n    \r\n    case 'UPDATE_PARAMETERS':\r\n      return {\r\n        ...state,\r\n        parameters: {\r\n          ...state.parameters,\r\n          ...action.payload,\r\n        },\r\n      };\r\n    \r\n    case 'SET_VALIDATION_ERRORS':\r\n      return {\r\n        ...state,\r\n        validationErrors: action.payload,\r\n      };\r\n    \r\n    case 'SET_VALIDATING':\r\n      return {\r\n        ...state,\r\n        isValidating: action.payload,\r\n      };\r\n    \r\n    case 'SET_CAN_START_ANALYSIS':\r\n      return {\r\n        ...state,\r\n        canStartAnalysis: action.payload,\r\n      };\r\n    \r\n    case 'RESET_PARAMETERS':\r\n      return {\r\n        ...state,\r\n        parameters: DEFAULT_PARAMETERS,\r\n        validationErrors: [],\r\n        canStartAnalysis: false,\r\n      };\r\n    \r\n    case 'SET_STEP':\r\n      return {\r\n        ...state,\r\n        step: action.payload,\r\n      };\r\n    \r\n    case 'SET_PARAMETER_OPTIONS':\r\n      return {\r\n        ...state,\r\n        parameterOptions: action.payload,\r\n      };\r\n    \r\n    default:\r\n      return state;\r\n  }\r\n}\r\n\r\nconst ParameterContext = createContext<ParameterContextType | undefined>(undefined);\r\n\r\nexport const useParameters = () => {\r\n  const context = useContext(ParameterContext);\r\n  if (!context) {\r\n    throw new Error('useParameters must be used within a ParameterProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface ParameterProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport const ParameterProvider: React.FC<ParameterProviderProps> = ({ children }) => {\r\n  const [state, dispatch] = useReducer(parameterReducer, initialState);\r\n\r\n  // Load parameter options on mount\r\n  useEffect(() => {\r\n    const loadParameterOptions = async () => {\r\n      try {\r\n        const response = await fetch('http://localhost:8000/api/parameters/options');\r\n        if (response.ok) {\r\n          const options: ParameterOptions = await response.json();\r\n          dispatch({ type: 'SET_PARAMETER_OPTIONS', payload: options });\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to load parameter options:', error);\r\n      }\r\n    };\r\n\r\n    loadParameterOptions();\r\n  }, []);\r\n\r\n  const updateFileInfo = useCallback((fileInfo: FileInfo) => {\r\n    dispatch({ type: 'SET_FILE_INFO', payload: fileInfo });\r\n  }, []);\r\n\r\n  const updateParameters = useCallback((parameters: Partial<AnalysisParameters>) => {\r\n    dispatch({ type: 'UPDATE_PARAMETERS', payload: parameters });\r\n  }, []);\r\n\r\n  const updateThresholds = useCallback((thresholds: Partial<AnalysisParameters['thresholds']>) => {\r\n    dispatch({ \r\n      type: 'UPDATE_PARAMETERS', \r\n      payload: { \r\n        thresholds: { ...state.parameters.thresholds, ...thresholds } \r\n      } \r\n    });\r\n  }, [state.parameters.thresholds]);\r\n\r\n  const updateMontage = useCallback((montage: Partial<AnalysisParameters['montage']>) => {\r\n    dispatch({ \r\n      type: 'UPDATE_PARAMETERS', \r\n      payload: { \r\n        montage: { ...state.parameters.montage, ...montage } \r\n      } \r\n    });\r\n  }, [state.parameters.montage]);\r\n\r\n  const updateFrequency = useCallback((frequency: Partial<AnalysisParameters['frequency']>) => {\r\n    dispatch({ \r\n      type: 'UPDATE_PARAMETERS', \r\n      payload: { \r\n        frequency: { ...state.parameters.frequency, ...frequency } \r\n      } \r\n    });\r\n  }, [state.parameters.frequency]);\r\n\r\n  const updateTimeSegment = useCallback((timeSegment: Partial<AnalysisParameters['time_segment']>) => {\r\n    dispatch({ \r\n      type: 'UPDATE_PARAMETERS', \r\n      payload: { \r\n        time_segment: { ...state.parameters.time_segment, ...timeSegment } \r\n      } \r\n    });\r\n  }, [state.parameters.time_segment]);\r\n\r\n  const updateChannelSelection = useCallback((channelSelection: Partial<AnalysisParameters['channel_selection']>) => {\r\n    dispatch({ \r\n      type: 'UPDATE_PARAMETERS', \r\n      payload: { \r\n        channel_selection: { ...state.parameters.channel_selection, ...channelSelection } \r\n      } \r\n    });\r\n  }, [state.parameters.channel_selection]);\r\n\r\n  const validateParameters = useCallback(async (): Promise<boolean> => {\r\n    if (!state.fileInfo) {\r\n      return false;\r\n    }\r\n\r\n    dispatch({ type: 'SET_VALIDATING', payload: true });\r\n\r\n    try {\r\n      const response = await fetch('http://localhost:8000/api/analyze/start', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          filepath: state.fileInfo.filepath,\r\n          parameters: {\r\n            thresholds: state.parameters.thresholds,\r\n            montage: state.parameters.montage,\r\n            frequency: state.parameters.frequency,\r\n            time_segment: state.parameters.time_segment,\r\n            channel_selection: state.parameters.channel_selection,\r\n          },\r\n        }),\r\n      });\r\n\r\n      if (response.ok) {\r\n        dispatch({ type: 'SET_VALIDATION_ERRORS', payload: [] });\r\n        dispatch({ type: 'SET_CAN_START_ANALYSIS', payload: true });\r\n        return true;\r\n      } else {\r\n        const errorData = await response.json();\r\n        const errors: ValidationError[] = errorData.errors || [\r\n          { field: 'general', message: errorData.detail || 'Validation failed' }\r\n        ];\r\n        dispatch({ type: 'SET_VALIDATION_ERRORS', payload: errors });\r\n        dispatch({ type: 'SET_CAN_START_ANALYSIS', payload: false });\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      const validationError: ValidationError = {\r\n        field: 'general',\r\n        message: error instanceof Error ? error.message : 'Validation failed'\r\n      };\r\n      dispatch({ type: 'SET_VALIDATION_ERRORS', payload: [validationError] });\r\n      dispatch({ type: 'SET_CAN_START_ANALYSIS', payload: false });\r\n      return false;\r\n    } finally {\r\n      dispatch({ type: 'SET_VALIDATING', payload: false });\r\n    }\r\n  }, [state.fileInfo, state.parameters]);\r\n\r\n  const resetParameters = useCallback(() => {\r\n    dispatch({ type: 'RESET_PARAMETERS' });\r\n  }, []);\r\n\r\n  const loadParameters = useCallback((parameters: AnalysisParameters) => {\r\n    dispatch({ type: 'UPDATE_PARAMETERS', payload: parameters });\r\n  }, []);\r\n\r\n  const setStep = useCallback((step: AnalysisState['step']) => {\r\n    dispatch({ type: 'SET_STEP', payload: step });\r\n  }, []);\r\n\r\n  // Auto-validate when parameters change (debounced)\r\n  useEffect(() => {\r\n    if (state.step === 'parameter_configuration' && state.fileInfo) {\r\n      const timeoutId = setTimeout(() => {\r\n        validateParameters();\r\n      }, 500); // 500ms debounce\r\n\r\n      return () => clearTimeout(timeoutId);\r\n    }\r\n  }, [state.parameters, state.step, state.fileInfo, validateParameters]);\r\n\r\n  const contextValue: ParameterContextType = {\r\n    state,\r\n    updateFileInfo,\r\n    updateParameters,\r\n    updateThresholds,\r\n    updateMontage,\r\n    updateFrequency,\r\n    updateTimeSegment,\r\n    updateChannelSelection,\r\n    validateParameters,\r\n    resetParameters,\r\n    loadParameters,\r\n    setStep,\r\n    parameterOptions: state.parameterOptions,\r\n  };\r\n\r\n  return (\r\n    <ParameterContext.Provider value={contextValue}>\r\n      {children}\r\n    </ParameterContext.Provider>\r\n  );\r\n};"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAsCA,MAAM,eAA8B;IAClC,MAAM;IACN,YAAY,mHAAA,CAAA,qBAAkB;IAC9B,kBAAkB,EAAE;IACpB,cAAc;IACd,kBAAkB;AACpB;AAEA,SAAS,iBAAiB,KAAoB,EAAE,MAAuB;IACrE,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,UAAU,OAAO,OAAO;gBACxB,MAAM;YACR;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,YAAY;oBACV,GAAG,MAAM,UAAU;oBACnB,GAAG,OAAO,OAAO;gBACnB;YACF;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,kBAAkB,OAAO,OAAO;YAClC;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,cAAc,OAAO,OAAO;YAC9B;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,kBAAkB,OAAO,OAAO;YAClC;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,YAAY,mHAAA,CAAA,qBAAkB;gBAC9B,kBAAkB,EAAE;gBACpB,kBAAkB;YACpB;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,OAAO,OAAO;YACtB;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,kBAAkB,OAAO,OAAO;YAClC;QAEF;YACE,OAAO;IACX;AACF;AAEA,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAoC;AAElE,MAAM,gBAAgB;IAC3B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,oBAAsD,CAAC,EAAE,QAAQ,EAAE;IAC9E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB;IAEvD,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB;YAC3B,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,UAA4B,MAAM,SAAS,IAAI;oBACrD,SAAS;wBAAE,MAAM;wBAAyB,SAAS;oBAAQ;gBAC7D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAS;IACtD,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,SAAS;YAAE,MAAM;YAAqB,SAAS;QAAW;IAC5D,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,SAAS;YACP,MAAM;YACN,SAAS;gBACP,YAAY;oBAAE,GAAG,MAAM,UAAU,CAAC,UAAU;oBAAE,GAAG,UAAU;gBAAC;YAC9D;QACF;IACF,GAAG;QAAC,MAAM,UAAU,CAAC,UAAU;KAAC;IAEhC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,SAAS;YACP,MAAM;YACN,SAAS;gBACP,SAAS;oBAAE,GAAG,MAAM,UAAU,CAAC,OAAO;oBAAE,GAAG,OAAO;gBAAC;YACrD;QACF;IACF,GAAG;QAAC,MAAM,UAAU,CAAC,OAAO;KAAC;IAE7B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,SAAS;YACP,MAAM;YACN,SAAS;gBACP,WAAW;oBAAE,GAAG,MAAM,UAAU,CAAC,SAAS;oBAAE,GAAG,SAAS;gBAAC;YAC3D;QACF;IACF,GAAG;QAAC,MAAM,UAAU,CAAC,SAAS;KAAC;IAE/B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,SAAS;YACP,MAAM;YACN,SAAS;gBACP,cAAc;oBAAE,GAAG,MAAM,UAAU,CAAC,YAAY;oBAAE,GAAG,WAAW;gBAAC;YACnE;QACF;IACF,GAAG;QAAC,MAAM,UAAU,CAAC,YAAY;KAAC;IAElC,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1C,SAAS;YACP,MAAM;YACN,SAAS;gBACP,mBAAmB;oBAAE,GAAG,MAAM,UAAU,CAAC,iBAAiB;oBAAE,GAAG,gBAAgB;gBAAC;YAClF;QACF;IACF,GAAG;QAAC,MAAM,UAAU,CAAC,iBAAiB;KAAC;IAEvC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,CAAC,MAAM,QAAQ,EAAE;YACnB,OAAO;QACT;QAEA,SAAS;YAAE,MAAM;YAAkB,SAAS;QAAK;QAEjD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2CAA2C;gBACtE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,MAAM,QAAQ,CAAC,QAAQ;oBACjC,YAAY;wBACV,YAAY,MAAM,UAAU,CAAC,UAAU;wBACvC,SAAS,MAAM,UAAU,CAAC,OAAO;wBACjC,WAAW,MAAM,UAAU,CAAC,SAAS;wBACrC,cAAc,MAAM,UAAU,CAAC,YAAY;wBAC3C,mBAAmB,MAAM,UAAU,CAAC,iBAAiB;oBACvD;gBACF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS;oBAAE,MAAM;oBAAyB,SAAS,EAAE;gBAAC;gBACtD,SAAS;oBAAE,MAAM;oBAA0B,SAAS;gBAAK;gBACzD,OAAO;YACT,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,SAA4B,UAAU,MAAM,IAAI;oBACpD;wBAAE,OAAO;wBAAW,SAAS,UAAU,MAAM,IAAI;oBAAoB;iBACtE;gBACD,SAAS;oBAAE,MAAM;oBAAyB,SAAS;gBAAO;gBAC1D,SAAS;oBAAE,MAAM;oBAA0B,SAAS;gBAAM;gBAC1D,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,MAAM,kBAAmC;gBACvC,OAAO;gBACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;YACA,SAAS;gBAAE,MAAM;gBAAyB,SAAS;oBAAC;iBAAgB;YAAC;YACrE,SAAS;gBAAE,MAAM;gBAA0B,SAAS;YAAM;YAC1D,OAAO;QACT,SAAU;YACR,SAAS;gBAAE,MAAM;gBAAkB,SAAS;YAAM;QACpD;IACF,GAAG;QAAC,MAAM,QAAQ;QAAE,MAAM,UAAU;KAAC;IAErC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,SAAS;YAAE,MAAM;QAAmB;IACtC,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,SAAS;YAAE,MAAM;YAAqB,SAAS;QAAW;IAC5D,GAAG,EAAE;IAEL,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3B,SAAS;YAAE,MAAM;YAAY,SAAS;QAAK;IAC7C,GAAG,EAAE;IAEL,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,IAAI,KAAK,6BAA6B,MAAM,QAAQ,EAAE;YAC9D,MAAM,YAAY,WAAW;gBAC3B;YACF,GAAG,MAAM,iBAAiB;YAE1B,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC,MAAM,UAAU;QAAE,MAAM,IAAI;QAAE,MAAM,QAAQ;QAAE;KAAmB;IAErE,MAAM,eAAqC;QACzC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,kBAAkB,MAAM,gBAAgB;IAC1C;IAEA,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/HFOThresholdPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Activity } from 'lucide-react';\r\n\r\nconst HFOThresholdPanel: React.FC = () => {\r\n  const { state, updateThresholds, parameterOptions } = useParameters();\r\n  const { thresholds } = state.parameters;\r\n\r\n  // Default threshold options matching the original backend\r\n  const defaultThresholdOptions = {\r\n    amplitude1: { min: 2, max: 5, default: 2, description: \"HFO amp >= energy signal by (times of std)\" },\r\n    amplitude2: { min: 2, max: 5, default: 2, description: \"HFO amp >= mean baseline signal by (times of std)\" },\r\n    peaks1: { min: 2, max: 8, default: 6, description: \"Number of peaks in HFO >= Amplitude 1\" },\r\n    peaks2: { min: 2, max: 6, default: 3, description: \"Number of peaks in HFO >= Amplitude 2\" },\r\n    duration: { min: 5, max: 15, default: 10, description: \"HFO length >= (ms)\" },\r\n    temporal_sync: { min: 5, max: 12, default: 10, description: \"Inter HFO interval in any channel <= (ms)\" },\r\n    spatial_sync: { min: 5, max: 12, default: 10, description: \"Inter HFO interval across channels <= (ms)\" }\r\n  };\r\n\r\n  const thresholdOptions = parameterOptions?.thresholds || defaultThresholdOptions;\r\n\r\n  const handleThresholdChange = (parameter: keyof typeof thresholds, value: number) => {\r\n    updateThresholds({ [parameter]: value });\r\n  };\r\n\r\n  const createDropdownOptions = (min: number, max: number) => {\r\n    const options = [];\r\n    for (let i = min; i <= max; i++) {\r\n      options.push(i);\r\n    }\r\n    return options;\r\n  };\r\n\r\n  const ThresholdRow = ({ \r\n    parameter, \r\n    label, \r\n    description \r\n  }: { \r\n    parameter: keyof typeof thresholds;\r\n    label: string;\r\n    description: string;\r\n  }) => {\r\n    const config = thresholdOptions[parameter];\r\n    const options = createDropdownOptions(config.min, config.max);\r\n\r\n    return (\r\n      <div className=\"flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0\">\r\n        <div className=\"flex-1 pr-4\">\r\n          <div className=\"font-medium text-gray-900 text-sm\">{label}</div>\r\n          <div className=\"text-xs text-gray-600 mt-1\">{description}</div>\r\n        </div>\r\n        <div className=\"flex-shrink-0\">\r\n          <select\r\n            value={thresholds[parameter]}\r\n            onChange={(e) => handleThresholdChange(parameter, parseInt(e.target.value))}\r\n            className=\"w-20 px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n          >\r\n            {options.map(option => (\r\n              <option key={option} value={option}>\r\n                {option}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n      <div className=\"flex items-center gap-3 mb-6\">\r\n        <Activity className=\"w-5 h-5 text-green-600\" />\r\n        <h2 className=\"text-lg font-semibold text-gray-900\">HFO Detection Thresholds</h2>\r\n      </div>\r\n\r\n      <div className=\"space-y-0\">\r\n        <ThresholdRow\r\n          parameter=\"amplitude1\"\r\n          label=\"Amplitude 1\"\r\n          description={thresholdOptions.amplitude1.description}\r\n        />\r\n        \r\n        <ThresholdRow\r\n          parameter=\"amplitude2\"\r\n          label=\"Amplitude 2\"\r\n          description={thresholdOptions.amplitude2.description}\r\n        />\r\n        \r\n        <ThresholdRow\r\n          parameter=\"peaks1\"\r\n          label=\"Peaks 1\"\r\n          description={thresholdOptions.peaks1.description}\r\n        />\r\n        \r\n        <ThresholdRow\r\n          parameter=\"peaks2\"\r\n          label=\"Peaks 2\"\r\n          description={thresholdOptions.peaks2.description}\r\n        />\r\n        \r\n        <ThresholdRow\r\n          parameter=\"duration\"\r\n          label=\"Duration\"\r\n          description={thresholdOptions.duration.description}\r\n        />\r\n        \r\n        <ThresholdRow\r\n          parameter=\"temporal_sync\"\r\n          label=\"Temporal Synchronization\"\r\n          description={thresholdOptions.temporal_sync.description}\r\n        />\r\n        \r\n        <ThresholdRow\r\n          parameter=\"spatial_sync\"\r\n          label=\"Spatial Synchronization\"\r\n          description={thresholdOptions.spatial_sync.description}\r\n        />\r\n      </div>\r\n\r\n      <div className=\"mt-4 p-3 bg-blue-50 rounded-md\">\r\n        <p className=\"text-xs text-blue-700\">\r\n          <span className=\"font-medium\">Note:</span> These thresholds control the sensitivity of HFO detection. \r\n          Lower values increase sensitivity but may include more noise. Higher values are more conservative.\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HFOThresholdPanel;"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,oBAA8B;IAClC,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IAClE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,UAAU;IAEvC,0DAA0D;IAC1D,MAAM,0BAA0B;QAC9B,YAAY;YAAE,KAAK;YAAG,KAAK;YAAG,SAAS;YAAG,aAAa;QAA6C;QACpG,YAAY;YAAE,KAAK;YAAG,KAAK;YAAG,SAAS;YAAG,aAAa;QAAoD;QAC3G,QAAQ;YAAE,KAAK;YAAG,KAAK;YAAG,SAAS;YAAG,aAAa;QAAwC;QAC3F,QAAQ;YAAE,KAAK;YAAG,KAAK;YAAG,SAAS;YAAG,aAAa;QAAwC;QAC3F,UAAU;YAAE,KAAK;YAAG,KAAK;YAAI,SAAS;YAAI,aAAa;QAAqB;QAC5E,eAAe;YAAE,KAAK;YAAG,KAAK;YAAI,SAAS;YAAI,aAAa;QAA4C;QACxG,cAAc;YAAE,KAAK;YAAG,KAAK;YAAI,SAAS;YAAI,aAAa;QAA6C;IAC1G;IAEA,MAAM,mBAAmB,kBAAkB,cAAc;IAEzD,MAAM,wBAAwB,CAAC,WAAoC;QACjE,iBAAiB;YAAE,CAAC,UAAU,EAAE;QAAM;IACxC;IAEA,MAAM,wBAAwB,CAAC,KAAa;QAC1C,MAAM,UAAU,EAAE;QAClB,IAAK,IAAI,IAAI,KAAK,KAAK,KAAK,IAAK;YAC/B,QAAQ,IAAI,CAAC;QACf;QACA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,KAAK,EACL,WAAW,EAKZ;QACC,MAAM,SAAS,gBAAgB,CAAC,UAAU;QAC1C,MAAM,UAAU,sBAAsB,OAAO,GAAG,EAAE,OAAO,GAAG;QAE5D,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAqC;;;;;;sCACpD,8OAAC;4BAAI,WAAU;sCAA8B;;;;;;;;;;;;8BAE/C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,OAAO,UAAU,CAAC,UAAU;wBAC5B,UAAU,CAAC,IAAM,sBAAsB,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;wBACzE,WAAU;kCAET,QAAQ,GAAG,CAAC,CAAA,uBACX,8OAAC;gCAAoB,OAAO;0CACzB;+BADU;;;;;;;;;;;;;;;;;;;;;IAQzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;;0BAGtD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,UAAU,CAAC,WAAW;;;;;;kCAGtD,8OAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,UAAU,CAAC,WAAW;;;;;;kCAGtD,8OAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,MAAM,CAAC,WAAW;;;;;;kCAGlD,8OAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,MAAM,CAAC,WAAW;;;;;;kCAGlD,8OAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,QAAQ,CAAC,WAAW;;;;;;kCAGpD,8OAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,aAAa,CAAC,WAAW;;;;;;kCAGzD,8OAAC;wBACC,WAAU;wBACV,OAAM;wBACN,aAAa,iBAAiB,YAAY,CAAC,WAAW;;;;;;;;;;;;0BAI1D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;4BAAK,WAAU;sCAAc;;;;;;wBAAY;;;;;;;;;;;;;;;;;;AAMpD;uCAEe", "debugId": null}}, {"offset": {"line": 1876, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/MontageSelectionPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Network } from 'lucide-react';\r\n\r\nconst MontageSelectionPanel: React.FC = () => {\r\n  const { state, updateMontage } = useParameters();\r\n  const { montage } = state.parameters;\r\n  const { fileInfo } = state;\r\n\r\n  const handleMontageTypeChange = (type: 'bipolar' | 'average' | 'referential') => {\r\n    updateMontage({ type, reference_channel: type === 'referential' ? montage.reference_channel : undefined });\r\n  };\r\n\r\n  const handleReferenceChannelChange = (referenceChannel: string) => {\r\n    updateMontage({ reference_channel: referenceChannel });\r\n  };\r\n\r\n  const availableChannels = fileInfo?.channels || [];\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n      <div className=\"flex items-center gap-3 mb-6\">\r\n        <Network className=\"w-5 h-5 text-purple-600\" />\r\n        <h2 className=\"text-lg font-semibold text-gray-900\">Select Montage for Analysis</h2>\r\n      </div>\r\n\r\n      <div className=\"space-y-4\">\r\n        {/* Bipolar Option */}\r\n        <label className=\"flex items-start gap-3 cursor-pointer\">\r\n          <input\r\n            type=\"radio\"\r\n            name=\"montage\"\r\n            value=\"bipolar\"\r\n            checked={montage.type === 'bipolar'}\r\n            onChange={() => handleMontageTypeChange('bipolar')}\r\n            className=\"mt-1 w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500\"\r\n          />\r\n          <div className=\"flex-1\">\r\n            <div className=\"font-medium text-gray-900\">Bipolar</div>\r\n            <div className=\"text-sm text-gray-600 mt-1\">\r\n              Each channel is referenced to its adjacent channel. Provides good localization and \r\n              cancels out common noise. Requires at least 2 channels.\r\n            </div>\r\n          </div>\r\n        </label>\r\n\r\n        {/* Average Option */}\r\n        <label className=\"flex items-start gap-3 cursor-pointer\">\r\n          <input\r\n            type=\"radio\"\r\n            name=\"montage\"\r\n            value=\"average\"\r\n            checked={montage.type === 'average'}\r\n            onChange={() => handleMontageTypeChange('average')}\r\n            className=\"mt-1 w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500\"\r\n          />\r\n          <div className=\"flex-1\">\r\n            <div className=\"font-medium text-gray-900\">Average Reference</div>\r\n            <div className=\"text-sm text-gray-600 mt-1\">\r\n              Each channel is referenced to the average of all channels. Useful when no neutral \r\n              reference is available.\r\n            </div>\r\n          </div>\r\n        </label>\r\n\r\n        {/* Referential Option */}\r\n        <label className=\"flex items-start gap-3 cursor-pointer\">\r\n          <input\r\n            type=\"radio\"\r\n            name=\"montage\"\r\n            value=\"referential\"\r\n            checked={montage.type === 'referential'}\r\n            onChange={() => handleMontageTypeChange('referential')}\r\n            className=\"mt-1 w-4 h-4 text-purple-600 border-gray-300 focus:ring-purple-500\"\r\n          />\r\n          <div className=\"flex-1\">\r\n            <div className=\"font-medium text-gray-900\">Referential</div>\r\n            <div className=\"text-sm text-gray-600 mt-1\">\r\n              All channels are referenced to a specific reference channel. Select the reference channel below.\r\n            </div>\r\n          </div>\r\n        </label>\r\n\r\n        {/* Reference Channel Selection */}\r\n        {montage.type === 'referential' && (\r\n          <div className=\"ml-7 mt-3 p-4 bg-gray-50 rounded-md\">\r\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n              Reference Channel\r\n            </label>\r\n            <select\r\n              value={montage.reference_channel || ''}\r\n              onChange={(e) => handleReferenceChannelChange(e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\r\n            >\r\n              <option value=\"\">Select reference channel...</option>\r\n              {availableChannels.map(channel => (\r\n                <option key={channel} value={channel}>\r\n                  {channel}\r\n                </option>\r\n              ))}\r\n            </select>\r\n            {!montage.reference_channel && (\r\n              <p className=\"text-xs text-red-600 mt-1\">\r\n                Please select a reference channel for referential montage\r\n              </p>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"mt-4 p-3 bg-purple-50 rounded-md\">\r\n        <p className=\"text-xs text-purple-700\">\r\n          <span className=\"font-medium\">Tip:</span> Bipolar montage is typically preferred for HFO detection \r\n          as it provides better spatial resolution and noise cancellation.\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MontageSelectionPanel;"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,wBAAkC;IACtC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IAC7C,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,UAAU;IACpC,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,MAAM,0BAA0B,CAAC;QAC/B,cAAc;YAAE;YAAM,mBAAmB,SAAS,gBAAgB,QAAQ,iBAAiB,GAAG;QAAU;IAC1G;IAEA,MAAM,+BAA+B,CAAC;QACpC,cAAc;YAAE,mBAAmB;QAAiB;IACtD;IAEA,MAAM,oBAAoB,UAAU,YAAY,EAAE;IAElD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wMAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;;0BAGtD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAM;gCACN,SAAS,QAAQ,IAAI,KAAK;gCAC1B,UAAU,IAAM,wBAAwB;gCACxC,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA4B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAQhD,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAM;gCACN,SAAS,QAAQ,IAAI,KAAK;gCAC1B,UAAU,IAAM,wBAAwB;gCACxC,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA4B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAQhD,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAM;gCACN,SAAS,QAAQ,IAAI,KAAK;gCAC1B,UAAU,IAAM,wBAAwB;gCACxC,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA4B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;oBAO/C,QAAQ,IAAI,KAAK,+BAChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,OAAO,QAAQ,iBAAiB,IAAI;gCACpC,UAAU,CAAC,IAAM,6BAA6B,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,kBAAkB,GAAG,CAAC,CAAA,wBACrB,8OAAC;4CAAqB,OAAO;sDAC1B;2CADU;;;;;;;;;;;4BAKhB,CAAC,QAAQ,iBAAiB,kBACzB,8OAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;0BAQjD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;4BAAK,WAAU;sCAAc;;;;;;wBAAW;;;;;;;;;;;;;;;;;;AAMnD;uCAEe", "debugId": null}}, {"offset": {"line": 2165, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/FrequencyFilterPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Zap, AlertTriangle } from 'lucide-react';\r\n\r\nconst FrequencyFilterPanel: React.FC = () => {\r\n  const { state, updateFrequency, parameterOptions } = useParameters();\r\n  const { frequency } = state.parameters;\r\n  const { fileInfo } = state;\r\n\r\n  // Default frequency options from original backend\r\n  const defaultFrequencyOptions = {\r\n    low_cutoff_options: [1, 4, 8, 14, 30, 50, 70, 80, 120, 200, 250],\r\n    high_cutoff_options: [4, 8, 14, 30, 50, 70, 80, 120, 160, 200, 300, 330, 600, 660],\r\n    default_low: 50,\r\n    default_high: 300\r\n  };\r\n\r\n  const frequencyOptions = parameterOptions?.frequency || defaultFrequencyOptions;\r\n  const samplingRate = fileInfo?.sampling_rate || 1000;\r\n  const maxRecommendedFreq = samplingRate / 3;\r\n\r\n  const handleLowCutoffChange = (lowCutoff: number) => {\r\n    updateFrequency({ low_cutoff: lowCutoff });\r\n  };\r\n\r\n  const handleHighCutoffChange = (highCutoff: number) => {\r\n    updateFrequency({ high_cutoff: highCutoff });\r\n  };\r\n\r\n  const isValidConfiguration = () => {\r\n    return frequency.low_cutoff < frequency.high_cutoff && frequency.high_cutoff <= maxRecommendedFreq;\r\n  };\r\n\r\n  const getValidationMessage = () => {\r\n    if (frequency.low_cutoff >= frequency.high_cutoff) {\r\n      return \"Low cutoff must be less than high cutoff\";\r\n    }\r\n    if (frequency.high_cutoff > maxRecommendedFreq) {\r\n      return `High cutoff should be ≤ ${maxRecommendedFreq.toFixed(0)}Hz (sampling rate / 3)`;\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const validationMessage = getValidationMessage();\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n      <div className=\"flex items-center gap-3 mb-6\">\r\n        <Zap className=\"w-5 h-5 text-yellow-600\" />\r\n        <h2 className=\"text-lg font-semibold text-gray-900\">Select Frequency Band for Analysis</h2>\r\n      </div>\r\n\r\n      <div className=\"space-y-6\">\r\n        {/* Low Cutoff Filter */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Low Cutoff Filter (Hz)\r\n          </label>\r\n          <select\r\n            value={frequency.low_cutoff}\r\n            onChange={(e) => handleLowCutoffChange(parseInt(e.target.value))}\r\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:ring-2 focus:ring-yellow-500 focus:border-transparent\"\r\n          >\r\n            {frequencyOptions.low_cutoff_options.map(option => (\r\n              <option key={option} value={option}>\r\n                {option} Hz\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* High Cutoff Filter */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            High Cutoff Filter (Hz)\r\n          </label>\r\n          <div className=\"text-xs text-gray-500 mb-2\">\r\n            High cutoff should be ≤ 1/3 of sampling rate ({maxRecommendedFreq.toFixed(0)}Hz)\r\n          </div>\r\n          <select\r\n            value={frequency.high_cutoff}\r\n            onChange={(e) => handleHighCutoffChange(parseInt(e.target.value))}\r\n            className={`w-full px-3 py-2 border rounded-md bg-white focus:ring-2 focus:ring-yellow-500 focus:border-transparent ${\r\n              frequency.high_cutoff > maxRecommendedFreq \r\n                ? 'border-red-300 bg-red-50' \r\n                : 'border-gray-300'\r\n            }`}\r\n          >\r\n            {frequencyOptions.high_cutoff_options.map(option => (\r\n              <option \r\n                key={option} \r\n                value={option}\r\n                disabled={option > maxRecommendedFreq}\r\n              >\r\n                {option} Hz {option > maxRecommendedFreq ? '(exceeds limit)' : ''}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n\r\n        {/* Current Configuration Display */}\r\n        <div className=\"p-4 bg-gray-50 rounded-md\">\r\n          <div className=\"text-sm text-gray-700\">\r\n            <div className=\"grid grid-cols-3 gap-4\">\r\n              <div>\r\n                <div className=\"font-medium\">Low Cutoff</div>\r\n                <div className=\"text-lg font-mono\">{frequency.low_cutoff} Hz</div>\r\n              </div>\r\n              <div>\r\n                <div className=\"font-medium\">High Cutoff</div>\r\n                <div className=\"text-lg font-mono\">{frequency.high_cutoff} Hz</div>\r\n              </div>\r\n              <div>\r\n                <div className=\"font-medium\">Bandwidth</div>\r\n                <div className=\"text-lg font-mono\">{frequency.high_cutoff - frequency.low_cutoff} Hz</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Validation Message */}\r\n        {validationMessage && (\r\n          <div className=\"flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-md\">\r\n            <AlertTriangle className=\"w-4 h-4 text-red-500 flex-shrink-0 mt-0.5\" />\r\n            <p className=\"text-sm text-red-700\">{validationMessage}</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Sampling Rate Info */}\r\n        <div className=\"p-3 bg-blue-50 rounded-md\">\r\n          <p className=\"text-xs text-blue-700\">\r\n            <span className=\"font-medium\">File Info:</span> Sampling rate is {samplingRate}Hz. \r\n            Maximum recommended high cutoff is {maxRecommendedFreq.toFixed(0)}Hz for optimal signal quality.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FrequencyFilterPanel;"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAJA;;;;AAMA,MAAM,uBAAiC;IACrC,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IACjE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,UAAU;IACtC,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,kDAAkD;IAClD,MAAM,0BAA0B;QAC9B,oBAAoB;YAAC;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;SAAI;QAChE,qBAAqB;YAAC;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QAClF,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,mBAAmB,kBAAkB,aAAa;IACxD,MAAM,eAAe,UAAU,iBAAiB;IAChD,MAAM,qBAAqB,eAAe;IAE1C,MAAM,wBAAwB,CAAC;QAC7B,gBAAgB;YAAE,YAAY;QAAU;IAC1C;IAEA,MAAM,yBAAyB,CAAC;QAC9B,gBAAgB;YAAE,aAAa;QAAW;IAC5C;IAEA,MAAM,uBAAuB;QAC3B,OAAO,UAAU,UAAU,GAAG,UAAU,WAAW,IAAI,UAAU,WAAW,IAAI;IAClF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,UAAU,UAAU,IAAI,UAAU,WAAW,EAAE;YACjD,OAAO;QACT;QACA,IAAI,UAAU,WAAW,GAAG,oBAAoB;YAC9C,OAAO,CAAC,wBAAwB,EAAE,mBAAmB,OAAO,CAAC,GAAG,sBAAsB,CAAC;QACzF;QACA,OAAO;IACT;IAEA,MAAM,oBAAoB;IAE1B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;kCACf,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;;0BAGtD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,OAAO,UAAU,UAAU;gCAC3B,UAAU,CAAC,IAAM,sBAAsB,SAAS,EAAE,MAAM,CAAC,KAAK;gCAC9D,WAAU;0CAET,iBAAiB,kBAAkB,CAAC,GAAG,CAAC,CAAA,uBACvC,8OAAC;wCAAoB,OAAO;;4CACzB;4CAAO;;uCADG;;;;;;;;;;;;;;;;kCAQnB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;;oCAA6B;oCACK,mBAAmB,OAAO,CAAC;oCAAG;;;;;;;0CAE/E,8OAAC;gCACC,OAAO,UAAU,WAAW;gCAC5B,UAAU,CAAC,IAAM,uBAAuB,SAAS,EAAE,MAAM,CAAC,KAAK;gCAC/D,WAAW,CAAC,wGAAwG,EAClH,UAAU,WAAW,GAAG,qBACpB,6BACA,mBACJ;0CAED,iBAAiB,mBAAmB,CAAC,GAAG,CAAC,CAAA,uBACxC,8OAAC;wCAEC,OAAO;wCACP,UAAU,SAAS;;4CAElB;4CAAO;4CAAK,SAAS,qBAAqB,oBAAoB;;uCAJ1D;;;;;;;;;;;;;;;;kCAWb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,8OAAC;gDAAI,WAAU;;oDAAqB,UAAU,UAAU;oDAAC;;;;;;;;;;;;;kDAE3D,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,8OAAC;gDAAI,WAAU;;oDAAqB,UAAU,WAAW;oDAAC;;;;;;;;;;;;;kDAE5D,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAc;;;;;;0DAC7B,8OAAC;gDAAI,WAAU;;oDAAqB,UAAU,WAAW,GAAG,UAAU,UAAU;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOxF,mCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;8CACX,8OAAC;oCAAK,WAAU;8CAAc;;;;;;gCAAiB;gCAAmB;gCAAa;gCAC3C,mBAAmB,OAAO,CAAC;gCAAG;;;;;;;;;;;;;;;;;;;;;;;;AAM9E;uCAEe", "debugId": null}}, {"offset": {"line": 2531, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/TimeSegmentPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Clock, Calendar } from 'lucide-react';\r\n\r\nconst TimeSegmentPanel: React.FC = () => {\r\n  const { state, updateTimeSegment } = useParameters();\r\n  const { time_segment } = state.parameters;\r\n  const { fileInfo } = state;\r\n\r\n  const handleModeChange = (mode: 'entire_file' | 'start_end_times' | 'start_time_duration') => {\r\n    updateTimeSegment({ mode });\r\n  };\r\n\r\n  const handleFieldChange = (field: string, value: string) => {\r\n    updateTimeSegment({ [field]: value });\r\n  };\r\n\r\n  const formatDateTime = (dateStr: string, timeStr: string) => {\r\n    if (!dateStr || !timeStr) return '';\r\n    try {\r\n      // Convert dd.mm.yy HH:MM:SS format to readable format\r\n      const [day, month, year] = dateStr.split('.');\r\n      const fullYear = year.length === 2 ? `20${year}` : year;\r\n      return `${day}/${month}/${fullYear} ${timeStr}`;\r\n    } catch {\r\n      return `${dateStr} ${timeStr}`;\r\n    }\r\n  };\r\n\r\n  const fileStart = fileInfo ? formatDateTime(fileInfo.start_date, fileInfo.start_time) : '';\r\n  const fileEnd = fileInfo ? formatDateTime(fileInfo.end_date, fileInfo.end_time) : '';\r\n  const fileDuration = fileInfo?.duration_seconds || 0;\r\n\r\n  const validateDateFormat = (date: string) => {\r\n    return /^\\d{2}\\.\\d{2}\\.\\d{2}$/.test(date);\r\n  };\r\n\r\n  const validateTimeFormat = (time: string) => {\r\n    return /^\\d{2}:\\d{2}:\\d{2}$/.test(time);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n      <div className=\"flex items-center gap-3 mb-6\">\r\n        <Clock className=\"w-5 h-5 text-indigo-600\" />\r\n        <h2 className=\"text-lg font-semibold text-gray-900\">Specify Time Segment</h2>\r\n      </div>\r\n\r\n      {/* File Duration Info */}\r\n      {fileInfo && (\r\n        <div className=\"mb-6 p-3 bg-indigo-50 rounded-md\">\r\n          <div className=\"text-sm text-indigo-700\">\r\n            <div className=\"font-medium mb-1\">File Information:</div>\r\n            <div>Start: {fileStart}</div>\r\n            <div>End: {fileEnd}</div>\r\n            <div>Duration: {fileDuration.toFixed(1)} seconds</div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"space-y-6\">\r\n        {/* Entire File Option */}\r\n        <label className=\"flex items-start gap-3 cursor-pointer\">\r\n          <input\r\n            type=\"radio\"\r\n            name=\"timeSegment\"\r\n            value=\"entire_file\"\r\n            checked={time_segment.mode === 'entire_file'}\r\n            onChange={() => handleModeChange('entire_file')}\r\n            className=\"mt-1 w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500\"\r\n          />\r\n          <div className=\"flex-1\">\r\n            <div className=\"font-medium text-gray-900\">Entire File</div>\r\n            <div className=\"text-sm text-gray-600 mt-1\">\r\n              Analyze the complete EDF file from start to end.\r\n            </div>\r\n          </div>\r\n        </label>\r\n\r\n        {/* Start/End Times Option */}\r\n        <div className=\"space-y-3\">\r\n          <label className=\"flex items-start gap-3 cursor-pointer\">\r\n            <input\r\n              type=\"radio\"\r\n              name=\"timeSegment\"\r\n              value=\"start_end_times\"\r\n              checked={time_segment.mode === 'start_end_times'}\r\n              onChange={() => handleModeChange('start_end_times')}\r\n              className=\"mt-1 w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500\"\r\n            />\r\n            <div className=\"flex-1\">\r\n              <div className=\"font-medium text-gray-900\">Start/End Times</div>\r\n              <div className=\"text-sm text-gray-600 mt-1\">\r\n                Specify exact start and end date/time for analysis.\r\n              </div>\r\n            </div>\r\n          </label>\r\n\r\n          {time_segment.mode === 'start_end_times' && (\r\n            <div className=\"ml-7 space-y-4 p-4 bg-gray-50 rounded-md\">\r\n              {/* Start Date/Time */}\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Start Date (dd.mm.yy)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"dd.mm.yy\"\r\n                    value={time_segment.start_date || ''}\r\n                    onChange={(e) => handleFieldChange('start_date', e.target.value)}\r\n                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${\r\n                      time_segment.start_date && !validateDateFormat(time_segment.start_date) \r\n                        ? 'border-red-300 bg-red-50' \r\n                        : 'border-gray-300'\r\n                    }`}\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Start Time (HH:MM:SS)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"HH:MM:SS\"\r\n                    value={time_segment.start_time || ''}\r\n                    onChange={(e) => handleFieldChange('start_time', e.target.value)}\r\n                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${\r\n                      time_segment.start_time && !validateTimeFormat(time_segment.start_time) \r\n                        ? 'border-red-300 bg-red-50' \r\n                        : 'border-gray-300'\r\n                    }`}\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              {/* End Date/Time */}\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    End Date (dd.mm.yy)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"dd.mm.yy\"\r\n                    value={time_segment.end_date || ''}\r\n                    onChange={(e) => handleFieldChange('end_date', e.target.value)}\r\n                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${\r\n                      time_segment.end_date && !validateDateFormat(time_segment.end_date) \r\n                        ? 'border-red-300 bg-red-50' \r\n                        : 'border-gray-300'\r\n                    }`}\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    End Time (HH:MM:SS)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"HH:MM:SS\"\r\n                    value={time_segment.end_time || ''}\r\n                    onChange={(e) => handleFieldChange('end_time', e.target.value)}\r\n                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${\r\n                      time_segment.end_time && !validateTimeFormat(time_segment.end_time) \r\n                        ? 'border-red-300 bg-red-50' \r\n                        : 'border-gray-300'\r\n                    }`}\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Start Time/Duration Option */}\r\n        <div className=\"space-y-3\">\r\n          <label className=\"flex items-start gap-3 cursor-pointer\">\r\n            <input\r\n              type=\"radio\"\r\n              name=\"timeSegment\"\r\n              value=\"start_time_duration\"\r\n              checked={time_segment.mode === 'start_time_duration'}\r\n              onChange={() => handleModeChange('start_time_duration')}\r\n              className=\"mt-1 w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500\"\r\n            />\r\n            <div className=\"flex-1\">\r\n              <div className=\"font-medium text-gray-900\">Start Time/Duration</div>\r\n              <div className=\"text-sm text-gray-600 mt-1\">\r\n                Specify start time and duration in seconds.\r\n              </div>\r\n            </div>\r\n          </label>\r\n\r\n          {time_segment.mode === 'start_time_duration' && (\r\n            <div className=\"ml-7 space-y-4 p-4 bg-gray-50 rounded-md\">\r\n              {/* Start Date/Time */}\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Start Date (dd.mm.yy)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"dd.mm.yy\"\r\n                    value={time_segment.start_date || ''}\r\n                    onChange={(e) => handleFieldChange('start_date', e.target.value)}\r\n                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${\r\n                      time_segment.start_date && !validateDateFormat(time_segment.start_date) \r\n                        ? 'border-red-300 bg-red-50' \r\n                        : 'border-gray-300'\r\n                    }`}\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Start Time (HH:MM:SS)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"HH:MM:SS\"\r\n                    value={time_segment.start_time || ''}\r\n                    onChange={(e) => handleFieldChange('start_time', e.target.value)}\r\n                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${\r\n                      time_segment.start_time && !validateTimeFormat(time_segment.start_time) \r\n                        ? 'border-red-300 bg-red-50' \r\n                        : 'border-gray-300'\r\n                    }`}\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              {/* Duration */}\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                  Duration (seconds)\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  min=\"1\"\r\n                  max={fileDuration}\r\n                  placeholder=\"Duration in seconds\"\r\n                  value={time_segment.duration_seconds || ''}\r\n                  onChange={(e) => handleFieldChange('duration_seconds', parseFloat(e.target.value))}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent\"\r\n                />\r\n                {fileDuration > 0 && (\r\n                  <p className=\"text-xs text-gray-500 mt-1\">\r\n                    Maximum duration: {fileDuration.toFixed(1)} seconds\r\n                  </p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TimeSegmentPanel;"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,mBAA6B;IACjC,MAAM,EAAE,KAAK,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,UAAU;IACzC,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;YAAE;QAAK;IAC3B;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,kBAAkB;YAAE,CAAC,MAAM,EAAE;QAAM;IACrC;IAEA,MAAM,iBAAiB,CAAC,SAAiB;QACvC,IAAI,CAAC,WAAW,CAAC,SAAS,OAAO;QACjC,IAAI;YACF,sDAAsD;YACtD,MAAM,CAAC,KAAK,OAAO,KAAK,GAAG,QAAQ,KAAK,CAAC;YACzC,MAAM,WAAW,KAAK,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE,MAAM,GAAG;YACnD,OAAO,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS;QACjD,EAAE,OAAM;YACN,OAAO,GAAG,QAAQ,CAAC,EAAE,SAAS;QAChC;IACF;IAEA,MAAM,YAAY,WAAW,eAAe,SAAS,UAAU,EAAE,SAAS,UAAU,IAAI;IACxF,MAAM,UAAU,WAAW,eAAe,SAAS,QAAQ,EAAE,SAAS,QAAQ,IAAI;IAClF,MAAM,eAAe,UAAU,oBAAoB;IAEnD,MAAM,qBAAqB,CAAC;QAC1B,OAAO,wBAAwB,IAAI,CAAC;IACtC;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,sBAAsB,IAAI,CAAC;IACpC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;;YAIrD,0BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAmB;;;;;;sCAClC,8OAAC;;gCAAI;gCAAQ;;;;;;;sCACb,8OAAC;;gCAAI;gCAAM;;;;;;;sCACX,8OAAC;;gCAAI;gCAAW,aAAa,OAAO,CAAC;gCAAG;;;;;;;;;;;;;;;;;;0BAK9C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAM;gCACN,SAAS,aAAa,IAAI,KAAK;gCAC/B,UAAU,IAAM,iBAAiB;gCACjC,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA4B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAOhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCACC,MAAK;wCACL,MAAK;wCACL,OAAM;wCACN,SAAS,aAAa,IAAI,KAAK;wCAC/B,UAAU,IAAM,iBAAiB;wCACjC,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA4B;;;;;;0DAC3C,8OAAC;gDAAI,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;4BAM/C,aAAa,IAAI,KAAK,mCACrB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,aAAa,UAAU,IAAI;wDAClC,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC/D,WAAW,CAAC,+FAA+F,EACzG,aAAa,UAAU,IAAI,CAAC,mBAAmB,aAAa,UAAU,IAClE,6BACA,mBACJ;;;;;;;;;;;;0DAGN,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,aAAa,UAAU,IAAI;wDAClC,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC/D,WAAW,CAAC,+FAA+F,EACzG,aAAa,UAAU,IAAI,CAAC,mBAAmB,aAAa,UAAU,IAClE,6BACA,mBACJ;;;;;;;;;;;;;;;;;;kDAMR,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,aAAa,QAAQ,IAAI;wDAChC,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC7D,WAAW,CAAC,+FAA+F,EACzG,aAAa,QAAQ,IAAI,CAAC,mBAAmB,aAAa,QAAQ,IAC9D,6BACA,mBACJ;;;;;;;;;;;;0DAGN,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,aAAa,QAAQ,IAAI;wDAChC,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC7D,WAAW,CAAC,+FAA+F,EACzG,aAAa,QAAQ,IAAI,CAAC,mBAAmB,aAAa,QAAQ,IAC9D,6BACA,mBACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCACC,MAAK;wCACL,MAAK;wCACL,OAAM;wCACN,SAAS,aAAa,IAAI,KAAK;wCAC/B,UAAU,IAAM,iBAAiB;wCACjC,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA4B;;;;;;0DAC3C,8OAAC;gDAAI,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;4BAM/C,aAAa,IAAI,KAAK,uCACrB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,aAAa,UAAU,IAAI;wDAClC,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC/D,WAAW,CAAC,+FAA+F,EACzG,aAAa,UAAU,IAAI,CAAC,mBAAmB,aAAa,UAAU,IAClE,6BACA,mBACJ;;;;;;;;;;;;0DAGN,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,aAAa,UAAU,IAAI;wDAClC,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC/D,WAAW,CAAC,+FAA+F,EACzG,aAAa,UAAU,IAAI,CAAC,mBAAmB,aAAa,UAAU,IAClE,6BACA,mBACJ;;;;;;;;;;;;;;;;;;kDAMR,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAK;gDACL,aAAY;gDACZ,OAAO,aAAa,gBAAgB,IAAI;gDACxC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK;gDAChF,WAAU;;;;;;4CAEX,eAAe,mBACd,8OAAC;gDAAE,WAAU;;oDAA6B;oDACrB,aAAa,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/D;uCAEe", "debugId": null}}, {"offset": {"line": 3082, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/ChannelSelectionPanel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Layers, CheckSquare, Square, AlertCircle } from 'lucide-react';\r\n\r\nconst ChannelSelectionPanel: React.FC = () => {\r\n  const { state, updateChannelSelection } = useParameters();\r\n  const { channel_selection } = state.parameters;\r\n  const { fileInfo } = state;\r\n\r\n  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());\r\n\r\n  // Extract channel groups from fileInfo or parse from channel list\r\n  const extractChannelGroups = () => {\r\n    if (fileInfo?.channel_groups) {\r\n      return fileInfo.channel_groups;\r\n    }\r\n\r\n    // Fallback: parse channel names to create groups\r\n    const groups: Record<string, number[]> = {};\r\n    const excludeList = ['EKG', 'REF', 'E', 'C']; // From original code\r\n\r\n    if (fileInfo?.channels) {\r\n      fileInfo.channels.forEach(channel => {\r\n        // Clean channel name and extract group\r\n        const match = channel.match(/^(?:POL |P )?(\\w+?)(\\d+)$/);\r\n        if (match) {\r\n          const [, groupName, contactNum] = match;\r\n          if (!excludeList.includes(groupName.toUpperCase())) {\r\n            if (!groups[groupName]) {\r\n              groups[groupName] = [];\r\n            }\r\n            groups[groupName].push(parseInt(contactNum));\r\n          }\r\n        }\r\n      });\r\n\r\n      // Sort contact numbers within each group\r\n      Object.keys(groups).forEach(group => {\r\n        groups[group].sort((a, b) => a - b);\r\n      });\r\n    }\r\n\r\n    return groups;\r\n  };\r\n\r\n  const channelGroups = extractChannelGroups();\r\n  const groupNames = Object.keys(channelGroups);\r\n\r\n  // Auto-expand groups that have contacts\r\n  useEffect(() => {\r\n    const groupsWithContacts = groupNames.filter(group => \r\n      channel_selection.selected_leads.includes(group)\r\n    );\r\n    setExpandedGroups(new Set([...expandedGroups, ...groupsWithContacts]));\r\n  }, [channel_selection.selected_leads]);\r\n\r\n  const handleLeadToggle = (leadName: string) => {\r\n    const isSelected = channel_selection.selected_leads.includes(leadName);\r\n    let newSelectedLeads;\r\n    let newContactSpecs = { ...channel_selection.contact_specifications };\r\n\r\n    if (isSelected) {\r\n      // Remove lead\r\n      newSelectedLeads = channel_selection.selected_leads.filter(lead => lead !== leadName);\r\n      delete newContactSpecs[leadName];\r\n    } else {\r\n      // Add lead\r\n      newSelectedLeads = [...channel_selection.selected_leads, leadName];\r\n      // Set default contact specification (all contacts in the group)\r\n      const contacts = channelGroups[leadName] || [];\r\n      if (contacts.length > 0) {\r\n        const min = Math.min(...contacts);\r\n        const max = Math.max(...contacts);\r\n        newContactSpecs[leadName] = contacts.length > 1 ? `${min}-${max}` : `${min}`;\r\n      }\r\n    }\r\n\r\n    updateChannelSelection({\r\n      selected_leads: newSelectedLeads,\r\n      contact_specifications: newContactSpecs\r\n    });\r\n  };\r\n\r\n  const handleContactSpecChange = (leadName: string, specification: string) => {\r\n    updateChannelSelection({\r\n      contact_specifications: {\r\n        ...channel_selection.contact_specifications,\r\n        [leadName]: specification\r\n      }\r\n    });\r\n  };\r\n\r\n  const handleSelectAll = () => {\r\n    const allLeads = groupNames;\r\n    const newContactSpecs: Record<string, string> = {};\r\n\r\n    // Create default contact specifications for all leads\r\n    allLeads.forEach(leadName => {\r\n      const contacts = channelGroups[leadName] || [];\r\n      if (contacts.length > 0) {\r\n        const min = Math.min(...contacts);\r\n        const max = Math.max(...contacts);\r\n        newContactSpecs[leadName] = contacts.length > 1 ? `${min}-${max}` : `${min}`;\r\n      }\r\n    });\r\n\r\n    updateChannelSelection({\r\n      selected_leads: allLeads,\r\n      contact_specifications: newContactSpecs\r\n    });\r\n\r\n    // Expand all groups\r\n    setExpandedGroups(new Set(allLeads));\r\n  };\r\n\r\n  const handleDeselectAll = () => {\r\n    updateChannelSelection({\r\n      selected_leads: [],\r\n      contact_specifications: {}\r\n    });\r\n  };\r\n\r\n  const toggleGroupExpansion = (groupName: string) => {\r\n    const newExpanded = new Set(expandedGroups);\r\n    if (newExpanded.has(groupName)) {\r\n      newExpanded.delete(groupName);\r\n    } else {\r\n      newExpanded.add(groupName);\r\n    }\r\n    setExpandedGroups(newExpanded);\r\n  };\r\n\r\n  const parseContactSpecification = (spec: string): number[] => {\r\n    if (!spec) return [];\r\n    \r\n    const contacts: number[] = [];\r\n    const parts = spec.split(',');\r\n    \r\n    for (const part of parts) {\r\n      const trimmed = part.trim();\r\n      if (trimmed.includes('-')) {\r\n        const [start, end] = trimmed.split('-').map(n => parseInt(n.trim()));\r\n        if (!isNaN(start) && !isNaN(end)) {\r\n          for (let i = start; i <= end; i++) {\r\n            contacts.push(i);\r\n          }\r\n        }\r\n      } else {\r\n        const num = parseInt(trimmed);\r\n        if (!isNaN(num)) {\r\n          contacts.push(num);\r\n        }\r\n      }\r\n    }\r\n    \r\n    return [...new Set(contacts)].sort((a, b) => a - b);\r\n  };\r\n\r\n  const validateContactSpec = (leadName: string, spec: string): string | null => {\r\n    if (!spec.trim()) return \"Contact specification required\";\r\n    \r\n    const contacts = parseContactSpecification(spec);\r\n    const availableContacts = channelGroups[leadName] || [];\r\n    \r\n    if (contacts.length === 0) return \"Invalid contact specification format\";\r\n    \r\n    const invalidContacts = contacts.filter(c => !availableContacts.includes(c));\r\n    if (invalidContacts.length > 0) {\r\n      return `Invalid contacts: ${invalidContacts.join(', ')}. Available: ${availableContacts.join(', ')}`;\r\n    }\r\n    \r\n    return null;\r\n  };\r\n\r\n  const getTotalSelectedChannels = () => {\r\n    let total = 0;\r\n    channel_selection.selected_leads.forEach(leadName => {\r\n      const spec = channel_selection.contact_specifications[leadName] || '';\r\n      const contacts = parseContactSpecification(spec);\r\n      total += contacts.length;\r\n    });\r\n    return total;\r\n  };\r\n\r\n  const totalSelected = getTotalSelectedChannels();\r\n  const hasMinimumChannels = totalSelected >= 2;\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n      <div className=\"flex items-center justify-between mb-6\">\r\n        <div className=\"flex items-center gap-3\">\r\n          <Layers className=\"w-5 h-5 text-teal-600\" />\r\n          <h2 className=\"text-lg font-semibold text-gray-900\">Select Contacts to Analyze</h2>\r\n        </div>\r\n        \r\n        <div className=\"flex items-center gap-3\">\r\n          <span className=\"text-sm text-gray-600\">\r\n            {totalSelected} channel{totalSelected !== 1 ? 's' : ''} selected\r\n          </span>\r\n          <button\r\n            onClick={handleSelectAll}\r\n            className=\"text-sm text-teal-600 hover:text-teal-700 font-medium\"\r\n          >\r\n            Select All\r\n          </button>\r\n          <button\r\n            onClick={handleDeselectAll}\r\n            className=\"text-sm text-gray-600 hover:text-gray-700 font-medium\"\r\n          >\r\n            Clear All\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {!hasMinimumChannels && (\r\n        <div className=\"mb-6 flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-md\">\r\n          <AlertCircle className=\"w-4 h-4 text-red-500 flex-shrink-0 mt-0.5\" />\r\n          <p className=\"text-sm text-red-700\">\r\n            At least 2 channels must be selected before starting the analysis.\r\n          </p>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"text-sm text-gray-600 mb-4\">\r\n        Enter individual contact numbers separated by comma or range separated by dash \r\n        (e.g., \"1-5,7,9\" for contacts 1,2,3,4,5,7,9)\r\n      </div>\r\n\r\n      {groupNames.length === 0 ? (\r\n        <div className=\"text-center py-8 text-gray-500\">\r\n          <Layers className=\"w-12 h-12 text-gray-300 mx-auto mb-3\" />\r\n          <p>No channel groups found in the selected file.</p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"space-y-3\">\r\n          {groupNames.map(groupName => {\r\n            const contacts = channelGroups[groupName] || [];\r\n            const isSelected = channel_selection.selected_leads.includes(groupName);\r\n            const isExpanded = expandedGroups.has(groupName);\r\n            const contactSpec = channel_selection.contact_specifications[groupName] || '';\r\n            const validationError = isSelected ? validateContactSpec(groupName, contactSpec) : null;\r\n            const selectedContacts = isSelected ? parseContactSpecification(contactSpec) : [];\r\n\r\n            return (\r\n              <div key={groupName} className=\"border border-gray-200 rounded-md\">\r\n                <div \r\n                  className={`p-4 cursor-pointer transition-colors ${\r\n                    isSelected ? 'bg-teal-50 border-teal-200' : 'bg-gray-50 hover:bg-gray-100'\r\n                  }`}\r\n                  onClick={() => toggleGroupExpansion(groupName)}\r\n                >\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <button\r\n                        onClick={(e) => {\r\n                          e.stopPropagation();\r\n                          handleLeadToggle(groupName);\r\n                        }}\r\n                        className=\"flex items-center justify-center w-5 h-5 transition-colors\"\r\n                      >\r\n                        {isSelected ? (\r\n                          <CheckSquare className=\"w-5 h-5 text-teal-600\" />\r\n                        ) : (\r\n                          <Square className=\"w-5 h-5 text-gray-400 hover:text-gray-600\" />\r\n                        )}\r\n                      </button>\r\n                      <div>\r\n                        <div className=\"font-medium text-gray-900\">\r\n                          {groupName} ({contacts.length} contacts)\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-600\">\r\n                          Available contacts: {contacts.join(', ')}\r\n                        </div>\r\n                        {isSelected && selectedContacts.length > 0 && (\r\n                          <div className=\"text-sm text-teal-700 mt-1\">\r\n                            Selected: {selectedContacts.join(', ')} ({selectedContacts.length} contacts)\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"text-sm text-gray-400\">\r\n                      {isExpanded ? '▼' : '▶'}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {isExpanded && isSelected && (\r\n                  <div className=\"p-4 border-t border-gray-200 bg-white\">\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Contact Specification for {groupName}\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={contactSpec}\r\n                      onChange={(e) => handleContactSpecChange(groupName, e.target.value)}\r\n                      placeholder={`e.g., 1-${contacts.length > 1 ? contacts[contacts.length - 1] : contacts[0]}`}\r\n                      className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-teal-500 focus:border-transparent ${\r\n                        validationError ? 'border-red-300 bg-red-50' : 'border-gray-300'\r\n                      }`}\r\n                    />\r\n                    {validationError && (\r\n                      <p className=\"text-xs text-red-600 mt-1\">{validationError}</p>\r\n                    )}\r\n                    <div className=\"text-xs text-gray-500 mt-2\">\r\n                      Examples: \"1-5\" (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"mt-6 p-3 bg-teal-50 rounded-md\">\r\n        <p className=\"text-xs text-teal-700\">\r\n          <span className=\"font-medium\">Note:</span> Channel selection determines which electrode contacts \r\n          will be analyzed for HFO detection. For bipolar montage, adjacent contacts will be subtracted. \r\n          Select contacts that cover the region of interest for your analysis.\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChannelSelectionPanel;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,wBAAkC;IACtC,MAAM,EAAE,KAAK,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IACtD,MAAM,EAAE,iBAAiB,EAAE,GAAG,MAAM,UAAU;IAC9C,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAEtE,kEAAkE;IAClE,MAAM,uBAAuB;QAC3B,IAAI,UAAU,gBAAgB;YAC5B,OAAO,SAAS,cAAc;QAChC;QAEA,iDAAiD;QACjD,MAAM,SAAmC,CAAC;QAC1C,MAAM,cAAc;YAAC;YAAO;YAAO;YAAK;SAAI,EAAE,qBAAqB;QAEnE,IAAI,UAAU,UAAU;YACtB,SAAS,QAAQ,CAAC,OAAO,CAAC,CAAA;gBACxB,uCAAuC;gBACvC,MAAM,QAAQ,QAAQ,KAAK,CAAC;gBAC5B,IAAI,OAAO;oBACT,MAAM,GAAG,WAAW,WAAW,GAAG;oBAClC,IAAI,CAAC,YAAY,QAAQ,CAAC,UAAU,WAAW,KAAK;wBAClD,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;4BACtB,MAAM,CAAC,UAAU,GAAG,EAAE;wBACxB;wBACA,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS;oBAClC;gBACF;YACF;YAEA,yCAAyC;YACzC,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;gBAC1B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;YACnC;QACF;QAEA,OAAO;IACT;IAEA,MAAM,gBAAgB;IACtB,MAAM,aAAa,OAAO,IAAI,CAAC;IAE/B,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,QAC3C,kBAAkB,cAAc,CAAC,QAAQ,CAAC;QAE5C,kBAAkB,IAAI,IAAI;eAAI;eAAmB;SAAmB;IACtE,GAAG;QAAC,kBAAkB,cAAc;KAAC;IAErC,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa,kBAAkB,cAAc,CAAC,QAAQ,CAAC;QAC7D,IAAI;QACJ,IAAI,kBAAkB;YAAE,GAAG,kBAAkB,sBAAsB;QAAC;QAEpE,IAAI,YAAY;YACd,cAAc;YACd,mBAAmB,kBAAkB,cAAc,CAAC,MAAM,CAAC,CAAA,OAAQ,SAAS;YAC5E,OAAO,eAAe,CAAC,SAAS;QAClC,OAAO;YACL,WAAW;YACX,mBAAmB;mBAAI,kBAAkB,cAAc;gBAAE;aAAS;YAClE,gEAAgE;YAChE,MAAM,WAAW,aAAa,CAAC,SAAS,IAAI,EAAE;YAC9C,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,MAAM,MAAM,KAAK,GAAG,IAAI;gBACxB,MAAM,MAAM,KAAK,GAAG,IAAI;gBACxB,eAAe,CAAC,SAAS,GAAG,SAAS,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE,KAAK,GAAG,GAAG,KAAK;YAC9E;QACF;QAEA,uBAAuB;YACrB,gBAAgB;YAChB,wBAAwB;QAC1B;IACF;IAEA,MAAM,0BAA0B,CAAC,UAAkB;QACjD,uBAAuB;YACrB,wBAAwB;gBACtB,GAAG,kBAAkB,sBAAsB;gBAC3C,CAAC,SAAS,EAAE;YACd;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,WAAW;QACjB,MAAM,kBAA0C,CAAC;QAEjD,sDAAsD;QACtD,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,WAAW,aAAa,CAAC,SAAS,IAAI,EAAE;YAC9C,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,MAAM,MAAM,KAAK,GAAG,IAAI;gBACxB,MAAM,MAAM,KAAK,GAAG,IAAI;gBACxB,eAAe,CAAC,SAAS,GAAG,SAAS,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE,KAAK,GAAG,GAAG,KAAK;YAC9E;QACF;QAEA,uBAAuB;YACrB,gBAAgB;YAChB,wBAAwB;QAC1B;QAEA,oBAAoB;QACpB,kBAAkB,IAAI,IAAI;IAC5B;IAEA,MAAM,oBAAoB;QACxB,uBAAuB;YACrB,gBAAgB,EAAE;YAClB,wBAAwB,CAAC;QAC3B;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,YAAY;YAC9B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,kBAAkB;IACpB;IAEA,MAAM,4BAA4B,CAAC;QACjC,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,MAAM,WAAqB,EAAE;QAC7B,MAAM,QAAQ,KAAK,KAAK,CAAC;QAEzB,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,UAAU,KAAK,IAAI;YACzB,IAAI,QAAQ,QAAQ,CAAC,MAAM;gBACzB,MAAM,CAAC,OAAO,IAAI,GAAG,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,SAAS,EAAE,IAAI;gBAChE,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,MAAM;oBAChC,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;wBACjC,SAAS,IAAI,CAAC;oBAChB;gBACF;YACF,OAAO;gBACL,MAAM,MAAM,SAAS;gBACrB,IAAI,CAAC,MAAM,MAAM;oBACf,SAAS,IAAI,CAAC;gBAChB;YACF;QACF;QAEA,OAAO;eAAI,IAAI,IAAI;SAAU,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IACnD;IAEA,MAAM,sBAAsB,CAAC,UAAkB;QAC7C,IAAI,CAAC,KAAK,IAAI,IAAI,OAAO;QAEzB,MAAM,WAAW,0BAA0B;QAC3C,MAAM,oBAAoB,aAAa,CAAC,SAAS,IAAI,EAAE;QAEvD,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;QAElC,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,kBAAkB,QAAQ,CAAC;QACzE,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,OAAO,CAAC,kBAAkB,EAAE,gBAAgB,IAAI,CAAC,MAAM,aAAa,EAAE,kBAAkB,IAAI,CAAC,OAAO;QACtG;QAEA,OAAO;IACT;IAEA,MAAM,2BAA2B;QAC/B,IAAI,QAAQ;QACZ,kBAAkB,cAAc,CAAC,OAAO,CAAC,CAAA;YACvC,MAAM,OAAO,kBAAkB,sBAAsB,CAAC,SAAS,IAAI;YACnE,MAAM,WAAW,0BAA0B;YAC3C,SAAS,SAAS,MAAM;QAC1B;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB;IACtB,MAAM,qBAAqB,iBAAiB;IAE5C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;;oCACb;oCAAc;oCAAS,kBAAkB,IAAI,MAAM;oCAAG;;;;;;;0CAEzD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;YAMJ,CAAC,oCACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;;0BAMxC,8OAAC;gBAAI,WAAU;0BAA6B;;;;;;YAK3C,WAAW,MAAM,KAAK,kBACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;kCAAE;;;;;;;;;;;yEAGL,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAA;oBACd,MAAM,WAAW,aAAa,CAAC,UAAU,IAAI,EAAE;oBAC/C,MAAM,aAAa,kBAAkB,cAAc,CAAC,QAAQ,CAAC;oBAC7D,MAAM,aAAa,eAAe,GAAG,CAAC;oBACtC,MAAM,cAAc,kBAAkB,sBAAsB,CAAC,UAAU,IAAI;oBAC3E,MAAM,kBAAkB,aAAa,oBAAoB,WAAW,eAAe;oBACnF,MAAM,mBAAmB,aAAa,0BAA0B,eAAe,EAAE;oBAEjF,qBACE,8OAAC;wBAAoB,WAAU;;0CAC7B,8OAAC;gCACC,WAAW,CAAC,qCAAqC,EAC/C,aAAa,+BAA+B,gCAC5C;gCACF,SAAS,IAAM,qBAAqB;0CAEpC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,iBAAiB;oDACnB;oDACA,WAAU;8DAET,2BACC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;iHAEvB,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAGtB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;;gEACZ;gEAAU;gEAAG,SAAS,MAAM;gEAAC;;;;;;;sEAEhC,8OAAC;4DAAI,WAAU;;gEAAwB;gEAChB,SAAS,IAAI,CAAC;;;;;;;wDAEpC,cAAc,iBAAiB,MAAM,GAAG,mBACvC,8OAAC;4DAAI,WAAU;;gEAA6B;gEAC/B,iBAAiB,IAAI,CAAC;gEAAM;gEAAG,iBAAiB,MAAM;gEAAC;;;;;;;;;;;;;;;;;;;sDAK1E,8OAAC;4CAAI,WAAU;sDACZ,aAAa,MAAM;;;;;;;;;;;;;;;;;4BAKzB,cAAc,4BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;4CAA+C;4CACnC;;;;;;;kDAE7B,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,wBAAwB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAClE,aAAa,CAAC,QAAQ,EAAE,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE;wCAC3F,WAAW,CAAC,6FAA6F,EACvG,kBAAkB,6BAA6B,mBAC/C;;;;;;oCAEH,iCACC,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAE5C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;uBA3DxC;;;;;gBAkEd;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;4BAAK,WAAU;sCAAc;;;;;;wBAAY;;;;;;;;;;;;;;;;;;AAOpD;uCAEe", "debugId": null}}, {"offset": {"line": 3607, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/ValidationDisplay.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Check<PERSON>ircle, AlertCircle, AlertTriangle, Loader } from 'lucide-react';\r\n\r\nconst ValidationDisplay: React.FC = () => {\r\n  const { state } = useParameters();\r\n  \r\n  const { validationErrors, isValidating, canStartAnalysis } = state;\r\n\r\n  if (!isValidating && validationErrors.length === 0 && !canStartAnalysis) {\r\n    return null; // Don't show anything if no validation has occurred\r\n  }\r\n\r\n  const getValidationIcon = () => {\r\n    if (isValidating) {\r\n      return <Loader className=\"w-5 h-5 text-blue-500 animate-spin\" />;\r\n    }\r\n    if (validationErrors.length > 0) {\r\n      return <AlertCircle className=\"w-5 h-5 text-red-500\" />;\r\n    }\r\n    if (canStartAnalysis) {\r\n      return <CheckCircle className=\"w-5 h-5 text-green-500\" />;\r\n    }\r\n    return <AlertTriangle className=\"w-5 h-5 text-yellow-500\" />;\r\n  };\r\n\r\n  const getValidationStatus = () => {\r\n    if (isValidating) {\r\n      return { title: 'Validating Parameters...', color: 'blue' };\r\n    }\r\n    if (validationErrors.length > 0) {\r\n      return { \r\n        title: `Validation Failed (${validationErrors.length} error${validationErrors.length > 1 ? 's' : ''})`, \r\n        color: 'red' \r\n      };\r\n    }\r\n    if (canStartAnalysis) {\r\n      return { title: 'Parameters Valid - Ready to Analyze', color: 'green' };\r\n    }\r\n    return { title: 'Parameters Not Validated', color: 'yellow' };\r\n  };\r\n\r\n  const status = getValidationStatus();\r\n\r\n  return (\r\n    <div className={`bg-white rounded-lg shadow-sm border-2 p-6 ${\r\n      status.color === 'red' ? 'border-red-200' :\r\n      status.color === 'green' ? 'border-green-200' :\r\n      status.color === 'blue' ? 'border-blue-200' :\r\n      'border-yellow-200'\r\n    }`}>\r\n      <div className=\"flex items-start gap-4\">\r\n        <div className=\"flex-shrink-0\">\r\n          {getValidationIcon()}\r\n        </div>\r\n        \r\n        <div className=\"flex-1\">\r\n          <h3 className={`text-lg font-semibold mb-2 ${\r\n            status.color === 'red' ? 'text-red-900' :\r\n            status.color === 'green' ? 'text-green-900' :\r\n            status.color === 'blue' ? 'text-blue-900' :\r\n            'text-yellow-900'\r\n          }`}>\r\n            {status.title}\r\n          </h3>\r\n\r\n          {isValidating && (\r\n            <div className=\"text-sm text-blue-700\">\r\n              <p>Checking parameter configuration against file properties and analysis requirements...</p>\r\n            </div>\r\n          )}\r\n\r\n          {validationErrors.length > 0 && (\r\n            <div className=\"space-y-3\">\r\n              <p className=\"text-sm text-red-700\">\r\n                Please fix the following issues before starting the analysis:\r\n              </p>\r\n              <div className=\"space-y-2\">\r\n                {validationErrors.map((error, index) => (\r\n                  <div key={index} className=\"bg-red-50 border border-red-200 rounded-md p-3\">\r\n                    <div className=\"flex items-start gap-2\">\r\n                      <AlertCircle className=\"w-4 h-4 text-red-500 flex-shrink-0 mt-0.5\" />\r\n                      <div>\r\n                        <div className=\"font-medium text-red-900 text-sm\">\r\n                          {error.field === 'general' ? 'General Error' : `${error.field.charAt(0).toUpperCase() + error.field.slice(1)} Error`}\r\n                        </div>\r\n                        <div className=\"text-sm text-red-700 mt-1\">\r\n                          {error.message}\r\n                        </div>\r\n                        {error.value && (\r\n                          <div className=\"text-xs text-red-600 mt-1 font-mono bg-red-100 px-2 py-1 rounded\">\r\n                            Current value: {JSON.stringify(error.value)}\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {canStartAnalysis && validationErrors.length === 0 && (\r\n            <div className=\"text-sm text-green-700\">\r\n              <p className=\"mb-2\">All parameters have been validated successfully!</p>\r\n              <div className=\"bg-green-50 border border-green-200 rounded-md p-3\">\r\n                <div className=\"text-xs text-green-800\">\r\n                  <div className=\"font-medium mb-2\">Validation Summary:</div>\r\n                  <ul className=\"space-y-1\">\r\n                    <li>✓ File format and header validated</li>\r\n                    <li>✓ Threshold parameters within valid ranges</li>\r\n                    <li>✓ Frequency filters compatible with sampling rate</li>\r\n                    <li>✓ Montage configuration valid</li>\r\n                    <li>✓ Time segment within file duration</li>\r\n                    <li>✓ Minimum 2 channels selected</li>\r\n                    <li>✓ Channel specifications valid</li>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {!isValidating && !canStartAnalysis && validationErrors.length === 0 && (\r\n            <div className=\"text-sm text-yellow-700\">\r\n              <p>Parameters will be validated automatically as you make changes. \r\n              Make sure to configure all required settings before starting the analysis.</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ValidationDisplay;"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAJA;;;;AAMA,MAAM,oBAA8B;IAClC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IAE9B,MAAM,EAAE,gBAAgB,EAAE,YAAY,EAAE,gBAAgB,EAAE,GAAG;IAE7D,IAAI,CAAC,gBAAgB,iBAAiB,MAAM,KAAK,KAAK,CAAC,kBAAkB;QACvE,OAAO,MAAM,oDAAoD;IACnE;IAEA,MAAM,oBAAoB;QACxB,IAAI,cAAc;YAChB,qBAAO,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC3B;QACA,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,qBAAO,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QACA,IAAI,kBAAkB;YACpB,qBAAO,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QACA,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;IAClC;IAEA,MAAM,sBAAsB;QAC1B,IAAI,cAAc;YAChB,OAAO;gBAAE,OAAO;gBAA4B,OAAO;YAAO;QAC5D;QACA,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,OAAO;gBACL,OAAO,CAAC,mBAAmB,EAAE,iBAAiB,MAAM,CAAC,MAAM,EAAE,iBAAiB,MAAM,GAAG,IAAI,MAAM,GAAG,CAAC,CAAC;gBACtG,OAAO;YACT;QACF;QACA,IAAI,kBAAkB;YACpB,OAAO;gBAAE,OAAO;gBAAuC,OAAO;YAAQ;QACxE;QACA,OAAO;YAAE,OAAO;YAA4B,OAAO;QAAS;IAC9D;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QAAI,WAAW,CAAC,2CAA2C,EAC1D,OAAO,KAAK,KAAK,QAAQ,mBACzB,OAAO,KAAK,KAAK,UAAU,qBAC3B,OAAO,KAAK,KAAK,SAAS,oBAC1B,qBACA;kBACA,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAGH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAW,CAAC,2BAA2B,EACzC,OAAO,KAAK,KAAK,QAAQ,iBACzB,OAAO,KAAK,KAAK,UAAU,mBAC3B,OAAO,KAAK,KAAK,SAAS,kBAC1B,mBACA;sCACC,OAAO,KAAK;;;;;;wBAGd,8BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;wBAIN,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAuB;;;;;;8CAGpC,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC;4CAAgB,WAAU;sDACzB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EACZ,MAAM,KAAK,KAAK,YAAY,kBAAkB,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;;;;;;0EAEtH,8OAAC;gEAAI,WAAU;0EACZ,MAAM,OAAO;;;;;;4DAEf,MAAM,KAAK,kBACV,8OAAC;gEAAI,WAAU;;oEAAmE;oEAChE,KAAK,SAAS,CAAC,MAAM,KAAK;;;;;;;;;;;;;;;;;;;2CAZ1C;;;;;;;;;;;;;;;;wBAuBjB,oBAAoB,iBAAiB,MAAM,KAAK,mBAC/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAO;;;;;;8CACpB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAmB;;;;;;0DAClC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAOb,CAAC,gBAAgB,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,mBACjE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;uCAEe", "debugId": null}}, {"offset": {"line": 3943, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/SettingsManager.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Save, Upload, RotateCcw, Settings, Download, FileText } from 'lucide-react';\r\nimport { SettingsData } from '@/types/eeg';\r\n\r\nconst SettingsManager: React.FC = () => {\r\n  const { state, loadParameters, resetParameters } = useParameters();\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [saveMessage, setSaveMessage] = useState<string | null>(null);\r\n\r\n  const generateSettingsKey = (): string => {\r\n    if (state.fileInfo) {\r\n      const filename = state.fileInfo.filename.replace(/\\.[^/.]+$/, \"\"); // Remove extension\r\n      return `hfo-settings-${filename}`;\r\n    }\r\n    return 'hfo-settings-default';\r\n  };\r\n\r\n  const saveSettings = () => {\r\n    try {\r\n      const settingsData: SettingsData = {\r\n        parameters: state.parameters,\r\n        timestamp: new Date().toISOString(),\r\n        filename: state.fileInfo?.filename || 'unknown'\r\n      };\r\n\r\n      const key = generateSettingsKey();\r\n      localStorage.setItem(key, JSON.stringify(settingsData));\r\n      \r\n      setSaveMessage('Settings saved successfully!');\r\n      setTimeout(() => setSaveMessage(null), 3000);\r\n      setIsMenuOpen(false);\r\n    } catch (error) {\r\n      setSaveMessage('Failed to save settings');\r\n      setTimeout(() => setSaveMessage(null), 3000);\r\n    }\r\n  };\r\n\r\n  const loadSettings = () => {\r\n    try {\r\n      const key = generateSettingsKey();\r\n      const savedData = localStorage.getItem(key);\r\n      \r\n      if (savedData) {\r\n        const settingsData: SettingsData = JSON.parse(savedData);\r\n        loadParameters(settingsData.parameters);\r\n        setSaveMessage('Settings loaded successfully!');\r\n        setTimeout(() => setSaveMessage(null), 3000);\r\n      } else {\r\n        setSaveMessage('No saved settings found for this file');\r\n        setTimeout(() => setSaveMessage(null), 3000);\r\n      }\r\n      setIsMenuOpen(false);\r\n    } catch (error) {\r\n      setSaveMessage('Failed to load settings');\r\n      setTimeout(() => setSaveMessage(null), 3000);\r\n    }\r\n  };\r\n\r\n  const exportSettings = () => {\r\n    try {\r\n      const settingsData: SettingsData = {\r\n        parameters: state.parameters,\r\n        timestamp: new Date().toISOString(),\r\n        filename: state.fileInfo?.filename || 'unknown'\r\n      };\r\n\r\n      const dataStr = JSON.stringify(settingsData, null, 2);\r\n      const dataBlob = new Blob([dataStr], { type: 'application/json' });\r\n      const url = URL.createObjectURL(dataBlob);\r\n      \r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = `hfo-settings-${state.fileInfo?.filename || 'export'}-${new Date().toISOString().split('T')[0]}.json`;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      URL.revokeObjectURL(url);\r\n\r\n      setSaveMessage('Settings exported successfully!');\r\n      setTimeout(() => setSaveMessage(null), 3000);\r\n      setIsMenuOpen(false);\r\n    } catch (error) {\r\n      setSaveMessage('Failed to export settings');\r\n      setTimeout(() => setSaveMessage(null), 3000);\r\n    }\r\n  };\r\n\r\n  const importSettings = () => {\r\n    const input = document.createElement('input');\r\n    input.type = 'file';\r\n    input.accept = '.json';\r\n    input.onchange = (e) => {\r\n      const file = (e.target as HTMLInputElement).files?.[0];\r\n      if (file) {\r\n        const reader = new FileReader();\r\n        reader.onload = (e) => {\r\n          try {\r\n            const content = e.target?.result as string;\r\n            const settingsData: SettingsData = JSON.parse(content);\r\n            loadParameters(settingsData.parameters);\r\n            setSaveMessage(`Settings imported from ${settingsData.filename}`);\r\n            setTimeout(() => setSaveMessage(null), 3000);\r\n          } catch (error) {\r\n            setSaveMessage('Failed to import settings - invalid file format');\r\n            setTimeout(() => setSaveMessage(null), 3000);\r\n          }\r\n        };\r\n        reader.readAsText(file);\r\n      }\r\n    };\r\n    input.click();\r\n    setIsMenuOpen(false);\r\n  };\r\n\r\n  const resetToDefaults = () => {\r\n    resetParameters();\r\n    setSaveMessage('Settings reset to defaults');\r\n    setTimeout(() => setSaveMessage(null), 3000);\r\n    setIsMenuOpen(false);\r\n  };\r\n\r\n  const hasSettings = () => {\r\n    const key = generateSettingsKey();\r\n    return localStorage.getItem(key) !== null;\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      <button\r\n        onClick={() => setIsMenuOpen(!isMenuOpen)}\r\n        className=\"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\"\r\n      >\r\n        <Settings className=\"w-4 h-4\" />\r\n        Settings\r\n      </button>\r\n\r\n      {isMenuOpen && (\r\n        <>\r\n          <div \r\n            className=\"fixed inset-0 z-10\" \r\n            onClick={() => setIsMenuOpen(false)}\r\n          />\r\n          <div className=\"absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-20\">\r\n            <div className=\"p-2\">\r\n              <div className=\"text-xs font-medium text-gray-500 px-2 py-1 mb-2\">\r\n                Local Settings ({state.fileInfo?.filename || 'No file'})\r\n              </div>\r\n              \r\n              <button\r\n                onClick={saveSettings}\r\n                className=\"w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors\"\r\n              >\r\n                <Save className=\"w-4 h-4\" />\r\n                Save Settings\r\n              </button>\r\n              \r\n              <button\r\n                onClick={loadSettings}\r\n                disabled={!hasSettings()}\r\n                className={`w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md transition-colors ${\r\n                  hasSettings() \r\n                    ? 'text-gray-700 hover:bg-gray-100' \r\n                    : 'text-gray-400 cursor-not-allowed'\r\n                }`}\r\n              >\r\n                <Upload className=\"w-4 h-4\" />\r\n                Load Settings\r\n                {hasSettings() && <span className=\"text-xs text-green-600 ml-auto\">✓</span>}\r\n              </button>\r\n\r\n              <div className=\"border-t border-gray-200 my-2\" />\r\n              \r\n              <div className=\"text-xs font-medium text-gray-500 px-2 py-1 mb-2\">\r\n                Import/Export\r\n              </div>\r\n              \r\n              <button\r\n                onClick={exportSettings}\r\n                className=\"w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors\"\r\n              >\r\n                <Download className=\"w-4 h-4\" />\r\n                Export Settings\r\n              </button>\r\n              \r\n              <button\r\n                onClick={importSettings}\r\n                className=\"w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors\"\r\n              >\r\n                <FileText className=\"w-4 h-4\" />\r\n                Import Settings\r\n              </button>\r\n\r\n              <div className=\"border-t border-gray-200 my-2\" />\r\n              \r\n              <button\r\n                onClick={resetToDefaults}\r\n                className=\"w-full flex items-center gap-3 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors\"\r\n              >\r\n                <RotateCcw className=\"w-4 h-4\" />\r\n                Reset to Defaults\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </>\r\n      )}\r\n\r\n      {saveMessage && (\r\n        <div className=\"fixed top-4 right-4 z-50 bg-white border border-gray-200 rounded-md shadow-lg px-4 py-3\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className={`w-2 h-2 rounded-full ${\r\n              saveMessage.includes('successfully') || saveMessage.includes('imported') \r\n                ? 'bg-green-500' \r\n                : 'bg-red-500'\r\n            }`} />\r\n            <span className=\"text-sm text-gray-900\">{saveMessage}</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsManager;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAOA,MAAM,kBAA4B;IAChC,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,sBAAsB;QAC1B,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,WAAW,MAAM,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,KAAK,mBAAmB;YACtF,OAAO,CAAC,aAAa,EAAE,UAAU;QACnC;QACA,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,eAA6B;gBACjC,YAAY,MAAM,UAAU;gBAC5B,WAAW,IAAI,OAAO,WAAW;gBACjC,UAAU,MAAM,QAAQ,EAAE,YAAY;YACxC;YAEA,MAAM,MAAM;YACZ,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;YAEzC,eAAe;YACf,WAAW,IAAM,eAAe,OAAO;YACvC,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,eAAe;YACf,WAAW,IAAM,eAAe,OAAO;QACzC;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM;YACZ,MAAM,YAAY,aAAa,OAAO,CAAC;YAEvC,IAAI,WAAW;gBACb,MAAM,eAA6B,KAAK,KAAK,CAAC;gBAC9C,eAAe,aAAa,UAAU;gBACtC,eAAe;gBACf,WAAW,IAAM,eAAe,OAAO;YACzC,OAAO;gBACL,eAAe;gBACf,WAAW,IAAM,eAAe,OAAO;YACzC;YACA,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,eAAe;YACf,WAAW,IAAM,eAAe,OAAO;QACzC;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,eAA6B;gBACjC,YAAY,MAAM,UAAU;gBAC5B,WAAW,IAAI,OAAO,WAAW;gBACjC,UAAU,MAAM,QAAQ,EAAE,YAAY;YACxC;YAEA,MAAM,UAAU,KAAK,SAAS,CAAC,cAAc,MAAM;YACnD,MAAM,WAAW,IAAI,KAAK;gBAAC;aAAQ,EAAE;gBAAE,MAAM;YAAmB;YAChE,MAAM,MAAM,IAAI,eAAe,CAAC;YAEhC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,CAAC,aAAa,EAAE,MAAM,QAAQ,EAAE,YAAY,SAAS,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YACrH,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;YAEpB,eAAe;YACf,WAAW,IAAM,eAAe,OAAO;YACvC,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,eAAe;YACf,WAAW,IAAM,eAAe,OAAO;QACzC;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,IAAI,GAAG;QACb,MAAM,MAAM,GAAG;QACf,MAAM,QAAQ,GAAG,CAAC;YAChB,MAAM,OAAO,AAAC,EAAE,MAAM,CAAsB,KAAK,EAAE,CAAC,EAAE;YACtD,IAAI,MAAM;gBACR,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,IAAI;wBACF,MAAM,UAAU,EAAE,MAAM,EAAE;wBAC1B,MAAM,eAA6B,KAAK,KAAK,CAAC;wBAC9C,eAAe,aAAa,UAAU;wBACtC,eAAe,CAAC,uBAAuB,EAAE,aAAa,QAAQ,EAAE;wBAChE,WAAW,IAAM,eAAe,OAAO;oBACzC,EAAE,OAAO,OAAO;wBACd,eAAe;wBACf,WAAW,IAAM,eAAe,OAAO;oBACzC;gBACF;gBACA,OAAO,UAAU,CAAC;YACpB;QACF;QACA,MAAM,KAAK;QACX,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB;QACA,eAAe;QACf,WAAW,IAAM,eAAe,OAAO;QACvC,cAAc;IAChB;IAEA,MAAM,cAAc;QAClB,MAAM,MAAM;QACZ,OAAO,aAAa,OAAO,CAAC,SAAS;IACvC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS,IAAM,cAAc,CAAC;gBAC9B,WAAU;;kCAEV,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAY;;;;;;;YAIjC,4BACC;;kCACE,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,cAAc;;;;;;kCAE/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCAAmD;wCAC/C,MAAM,QAAQ,EAAE,YAAY;wCAAU;;;;;;;8CAGzD,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAI9B,8OAAC;oCACC,SAAS;oCACT,UAAU,CAAC;oCACX,WAAW,CAAC,8EAA8E,EACxF,gBACI,oCACA,oCACJ;;sDAEF,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;wCAE7B,+BAAiB,8OAAC;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;8CAGrE,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAIlE,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAIlC,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;YAQ1C,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAW,CAAC,qBAAqB,EACpC,YAAY,QAAQ,CAAC,mBAAmB,YAAY,QAAQ,CAAC,cACzD,iBACA,cACJ;;;;;;sCACF,8OAAC;4BAAK,WAAU;sCAAyB;;;;;;;;;;;;;;;;;;;;;;;AAMrD;uCAEe", "debugId": null}}, {"offset": {"line": 4297, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useParameters } from '@/contexts/ParameterContext';\r\nimport { Settings, Play, RotateCcw, Save, Upload } from 'lucide-react';\r\nimport HFOThresholdPanel from './HFOThresholdPanel';\r\nimport MontageSelectionPanel from './MontageSelectionPanel';\r\nimport FrequencyFilterPanel from './FrequencyFilterPanel';\r\nimport TimeSegmentPanel from './TimeSegmentPanel';\r\nimport ChannelSelectionPanel from './ChannelSelectionPanel';\r\nimport ValidationDisplay from './ValidationDisplay';\r\nimport SettingsManager from './SettingsManager';\r\n\r\ninterface ParameterConfigurationPanelProps {\r\n  onStartAnalysis: () => void;\r\n}\r\n\r\nconst ParameterConfigurationPanel: React.FC<ParameterConfigurationPanelProps> = ({ onStartAnalysis }) => {\r\n  const { \r\n    state, \r\n    resetParameters, \r\n    validateParameters,\r\n    setStep \r\n  } = useParameters();\r\n\r\n  const handleStartAnalysis = async () => {\r\n    const isValid = await validateParameters();\r\n    if (isValid) {\r\n      setStep('analysis');\r\n      onStartAnalysis();\r\n    }\r\n  };\r\n\r\n  const handleBackToFileSelection = () => {\r\n    setStep('file_selection');\r\n  };\r\n\r\n  if (!state.fileInfo) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-96\">\r\n        <div className=\"text-center\">\r\n          <Upload className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No File Selected</h3>\r\n          <p className=\"text-sm text-gray-600\">Please select an EDF file to configure parameters.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const { fileInfo } = state;\r\n\r\n  return (\r\n    <div className=\"max-w-7xl mx-auto p-6 space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <div className=\"flex items-center gap-3 mb-2\">\r\n              <Settings className=\"w-6 h-6 text-blue-600\" />\r\n              <h1 className=\"text-2xl font-bold text-gray-900\">HFO Analysis Configuration</h1>\r\n            </div>\r\n            <div className=\"text-sm text-gray-600 space-y-1\">\r\n              <p><span className=\"font-medium\">File:</span> {fileInfo.filename}</p>\r\n              <p><span className=\"font-medium\">Duration:</span> {fileInfo.duration_seconds.toFixed(1)}s</p>\r\n              <p><span className=\"font-medium\">Sampling Rate:</span> {fileInfo.sampling_rate}Hz</p>\r\n              <p><span className=\"font-medium\">Channels:</span> {fileInfo.channels.length}</p>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"flex items-center gap-3\">\r\n            <SettingsManager />\r\n            <button\r\n              onClick={resetParameters}\r\n              className=\"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\"\r\n            >\r\n              <RotateCcw className=\"w-4 h-4\" />\r\n              Reset\r\n            </button>\r\n            <button\r\n              onClick={handleBackToFileSelection}\r\n              className=\"px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\"\r\n            >\r\n              Back to File Selection\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Configuration Panels Grid */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        {/* Left Column */}\r\n        <div className=\"space-y-6\">\r\n          <HFOThresholdPanel />\r\n          <MontageSelectionPanel />\r\n        </div>\r\n\r\n        {/* Right Column */}\r\n        <div className=\"space-y-6\">\r\n          <FrequencyFilterPanel />\r\n          <TimeSegmentPanel />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Full Width Panels */}\r\n      <div className=\"space-y-6\">\r\n        <ChannelSelectionPanel />\r\n        <ValidationDisplay />\r\n      </div>\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"text-sm text-gray-600\">\r\n            {state.isValidating ? (\r\n              <div className=\"flex items-center gap-2\">\r\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"></div>\r\n                <span>Validating parameters...</span>\r\n              </div>\r\n            ) : state.validationErrors.length > 0 ? (\r\n              <span className=\"text-red-600\">\r\n                {state.validationErrors.length} validation error{state.validationErrors.length > 1 ? 's' : ''} found\r\n              </span>\r\n            ) : state.canStartAnalysis ? (\r\n              <span className=\"text-green-600\">Parameters validated - ready to analyze</span>\r\n            ) : (\r\n              <span>Configure parameters and validate before starting analysis</span>\r\n            )}\r\n          </div>\r\n\r\n          <button\r\n            onClick={handleStartAnalysis}\r\n            disabled={!state.canStartAnalysis || state.isValidating}\r\n            className={`flex items-center gap-2 px-6 py-3 rounded-md font-medium transition-all ${\r\n              state.canStartAnalysis && !state.isValidating\r\n                ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg'\r\n                : 'bg-gray-200 text-gray-500 cursor-not-allowed'\r\n            }`}\r\n          >\r\n            <Play className=\"w-5 h-5\" />\r\n            Start HFO Analysis\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ParameterConfigurationPanel;"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAiBA,MAAM,8BAA0E,CAAC,EAAE,eAAe,EAAE;IAClG,MAAM,EACJ,KAAK,EACL,eAAe,EACf,kBAAkB,EAClB,OAAO,EACR,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IAEhB,MAAM,sBAAsB;QAC1B,MAAM,UAAU,MAAM;QACtB,IAAI,SAAS;YACX,QAAQ;YACR;QACF;IACF;IAEA,MAAM,4BAA4B;QAChC,QAAQ;IACV;IAEA,IAAI,CAAC,MAAM,QAAQ,EAAE;QACnB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;;;;;;;8CAEnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAE,8OAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAY;gDAAE,SAAS,QAAQ;;;;;;;sDAChE,8OAAC;;8DAAE,8OAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAgB;gDAAE,SAAS,gBAAgB,CAAC,OAAO,CAAC;gDAAG;;;;;;;sDACxF,8OAAC;;8DAAE,8OAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAqB;gDAAE,SAAS,aAAa;gDAAC;;;;;;;sDAC/E,8OAAC;;8DAAE,8OAAC;oDAAK,WAAU;8DAAc;;;;;;gDAAgB;gDAAE,SAAS,QAAQ,CAAC,MAAM;;;;;;;;;;;;;;;;;;;sCAI/E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,+JAAA,CAAA,UAAe;;;;;8CAChB,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGnC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iKAAA,CAAA,UAAiB;;;;;0CAClB,8OAAC,qKAAA,CAAA,UAAqB;;;;;;;;;;;kCAIxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oKAAA,CAAA,UAAoB;;;;;0CACrB,8OAAC,gKAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;;0BAKrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,qKAAA,CAAA,UAAqB;;;;;kCACtB,8OAAC,iKAAA,CAAA,UAAiB;;;;;;;;;;;0BAIpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,MAAM,YAAY,iBACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;2EAEN,MAAM,gBAAgB,CAAC,MAAM,GAAG,kBAClC,8OAAC;gCAAK,WAAU;;oCACb,MAAM,gBAAgB,CAAC,MAAM;oCAAC;oCAAkB,MAAM,gBAAgB,CAAC,MAAM,GAAG,IAAI,MAAM;oCAAG;;;;;;2EAE9F,MAAM,gBAAgB,iBACxB,8OAAC;gCAAK,WAAU;0CAAiB;;;;;yFAEjC,8OAAC;0CAAK;;;;;;;;;;;sCAIV,8OAAC;4BACC,SAAS;4BACT,UAAU,CAAC,MAAM,gBAAgB,IAAI,MAAM,YAAY;4BACvD,WAAW,CAAC,wEAAwE,EAClF,MAAM,gBAAgB,IAAI,CAAC,MAAM,YAAY,GACzC,uEACA,gDACJ;;8CAEF,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;uCAEe", "debugId": null}}, {"offset": {"line": 4721, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Activity } from \"lucide-react\";\nimport EEG<PERSON>iewer from \"@/components/EEGViewer\";\nimport FileUploadCard from \"@/components/FileUploadCard\";\nimport ParameterConfigurationPanel from \"@/components/ParameterConfiguration\";\nimport { WebSocketProvider } from \"@/contexts/WebSocketContext\";\nimport { ParameterProvider, useParameters } from \"@/contexts/ParameterContext\";\nimport { FileInfo } from \"@/types/eeg\";\n\nfunction AppContent() {\n  const { state, updateFileInfo, setStep } = useParameters();\n  const [error, setError] = useState(\"\");\n\n  const handleFileSelect = async (filepath: string) => {\n    if (!filepath) {\n      setError(\"Please enter a file path\");\n      return;\n    }\n\n    try {\n      setError(\"\");\n      \n      // Call the backend to validate the file and get file info\n      const response = await fetch(\"http://localhost:8000/api/analyze\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ filepath }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || \"Failed to load file\");\n      }\n\n      const data = await response.json();\n      \n      // Extract file info from response\n      const fileInfo: FileInfo = {\n        filename: filepath.split('/').pop() || filepath,\n        filepath: filepath,\n        start_date: data.file_info.start_date,\n        start_time: data.file_info.start_time,\n        end_date: calculateEndDate(data.file_info.start_date, data.file_info.start_time, data.file_info.duration_seconds),\n        end_time: calculateEndTime(data.file_info.start_time, data.file_info.duration_seconds),\n        sampling_rate: data.file_info.sampling_rate,\n        max_frequency: data.file_info.sampling_rate / 3,\n        channels: data.file_info.channels,\n        channel_groups: extractChannelGroups(data.file_info.channels),\n        duration_seconds: data.file_info.duration_seconds\n      };\n\n      updateFileInfo(fileInfo);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"Failed to load file\");\n    }\n  };\n\n  const handleStartAnalysis = () => {\n    setStep('analysis');\n  };\n\n  // Helper functions for date/time calculations\n  const calculateEndDate = (startDate: string, startTime: string, durationSeconds: number): string => {\n    try {\n      // Convert dd.mm.yy to Date\n      const [day, month, year] = startDate.split('.');\n      const [hour, minute, second] = startTime.split(/[.:]/).map(n => parseInt(n));\n      \n      const startDateTime = new Date(2000 + parseInt(year), parseInt(month) - 1, parseInt(day), hour, minute, second);\n      const endDateTime = new Date(startDateTime.getTime() + durationSeconds * 1000);\n      \n      return endDateTime.toLocaleDateString('en-GB', { \n        day: '2-digit', \n        month: '2-digit', \n        year: '2-digit' \n      }).replace(/\\//g, '.');\n    } catch {\n      return startDate; // Fallback\n    }\n  };\n\n  const calculateEndTime = (startTime: string, durationSeconds: number): string => {\n    try {\n      const [hour, minute, second] = startTime.split(/[.:]/).map(n => parseInt(n));\n      const startDateTime = new Date();\n      startDateTime.setHours(hour, minute, second);\n      \n      const endDateTime = new Date(startDateTime.getTime() + durationSeconds * 1000);\n      \n      return endDateTime.toTimeString().slice(0, 8);\n    } catch {\n      return startTime; // Fallback\n    }\n  };\n\n  const extractChannelGroups = (channels: string[]): Record<string, number[]> => {\n    const groups: Record<string, number[]> = {};\n    const excludeList = ['EKG', 'REF', 'E', 'C'];\n\n    channels.forEach(channel => {\n      const match = channel.match(/^(?:POL |P )?(\\w+?)(\\d+)$/);\n      if (match) {\n        const [, groupName, contactNum] = match;\n        if (!excludeList.includes(groupName.toUpperCase())) {\n          if (!groups[groupName]) {\n            groups[groupName] = [];\n          }\n          groups[groupName].push(parseInt(contactNum));\n        }\n      }\n    });\n\n    // Sort contact numbers within each group\n    Object.keys(groups).forEach(group => {\n      groups[group].sort((a, b) => a - b);\n    });\n\n    return groups;\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {state.step === 'file_selection' && (\n        <main className=\"mx-auto px-6 py-8 min-h-screen\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-8\">\n              <div className=\"flex items-center justify-center gap-3 mb-4\">\n                <Activity className=\"w-8 h-8 text-blue-600\" />\n                <h1 className=\"text-3xl font-bold text-gray-900\">Biormika HFO Detector</h1>\n              </div>\n              <p className=\"text-lg text-gray-600\">\n                High-Frequency Oscillation Detection and Analysis\n              </p>\n            </div>\n            <FileUploadCard \n              onFileSelect={() => {}} // Not used in new flow\n              onStartAnalysis={() => {}} // Not used in new flow\n              error={error}\n              onFileValidated={handleFileSelect}\n            />\n            \n          </div>\n        </main>\n      )}\n\n      {state.step === 'parameter_configuration' && (\n        <ParameterConfigurationPanel onStartAnalysis={handleStartAnalysis} />\n      )}\n\n      {state.step === 'analysis' && (\n        <WebSocketProvider>\n          <EEGViewer />\n        </WebSocketProvider>\n      )}\n    </div>\n  );\n}\n\nexport default function Home() {\n  return (\n    <ParameterProvider>\n      <AppContent />\n    </ParameterProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAWA,SAAS;IACP,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,UAAU;YACb,SAAS;YACT;QACF;QAEA,IAAI;YACF,SAAS;YAET,0DAA0D;YAC1D,MAAM,WAAW,MAAM,MAAM,qCAAqC;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;YACtC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,kCAAkC;YAClC,MAAM,WAAqB;gBACzB,UAAU,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;gBACvC,UAAU;gBACV,YAAY,KAAK,SAAS,CAAC,UAAU;gBACrC,YAAY,KAAK,SAAS,CAAC,UAAU;gBACrC,UAAU,iBAAiB,KAAK,SAAS,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC,gBAAgB;gBAChH,UAAU,iBAAiB,KAAK,SAAS,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC,gBAAgB;gBACrF,eAAe,KAAK,SAAS,CAAC,aAAa;gBAC3C,eAAe,KAAK,SAAS,CAAC,aAAa,GAAG;gBAC9C,UAAU,KAAK,SAAS,CAAC,QAAQ;gBACjC,gBAAgB,qBAAqB,KAAK,SAAS,CAAC,QAAQ;gBAC5D,kBAAkB,KAAK,SAAS,CAAC,gBAAgB;YACnD;YAEA,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,sBAAsB;QAC1B,QAAQ;IACV;IAEA,8CAA8C;IAC9C,MAAM,mBAAmB,CAAC,WAAmB,WAAmB;QAC9D,IAAI;YACF,2BAA2B;YAC3B,MAAM,CAAC,KAAK,OAAO,KAAK,GAAG,UAAU,KAAK,CAAC;YAC3C,MAAM,CAAC,MAAM,QAAQ,OAAO,GAAG,UAAU,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAA,IAAK,SAAS;YAEzE,MAAM,gBAAgB,IAAI,KAAK,OAAO,SAAS,OAAO,SAAS,SAAS,GAAG,SAAS,MAAM,MAAM,QAAQ;YACxG,MAAM,cAAc,IAAI,KAAK,cAAc,OAAO,KAAK,kBAAkB;YAEzE,OAAO,YAAY,kBAAkB,CAAC,SAAS;gBAC7C,KAAK;gBACL,OAAO;gBACP,MAAM;YACR,GAAG,OAAO,CAAC,OAAO;QACpB,EAAE,OAAM;YACN,OAAO,WAAW,WAAW;QAC/B;IACF;IAEA,MAAM,mBAAmB,CAAC,WAAmB;QAC3C,IAAI;YACF,MAAM,CAAC,MAAM,QAAQ,OAAO,GAAG,UAAU,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAA,IAAK,SAAS;YACzE,MAAM,gBAAgB,IAAI;YAC1B,cAAc,QAAQ,CAAC,MAAM,QAAQ;YAErC,MAAM,cAAc,IAAI,KAAK,cAAc,OAAO,KAAK,kBAAkB;YAEzE,OAAO,YAAY,YAAY,GAAG,KAAK,CAAC,GAAG;QAC7C,EAAE,OAAM;YACN,OAAO,WAAW,WAAW;QAC/B;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,SAAmC,CAAC;QAC1C,MAAM,cAAc;YAAC;YAAO;YAAO;YAAK;SAAI;QAE5C,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,QAAQ,QAAQ,KAAK,CAAC;YAC5B,IAAI,OAAO;gBACT,MAAM,GAAG,WAAW,WAAW,GAAG;gBAClC,IAAI,CAAC,YAAY,QAAQ,CAAC,UAAU,WAAW,KAAK;oBAClD,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;wBACtB,MAAM,CAAC,UAAU,GAAG,EAAE;oBACxB;oBACA,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS;gBAClC;YACF;QACF;QAEA,yCAAyC;QACzC,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QACnC;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,MAAM,IAAI,KAAK,kCACd,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;;;;;;;8CAEnD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC,oIAAA,CAAA,UAAc;4BACb,cAAc,KAAO;4BACrB,iBAAiB,KAAO;4BACxB,OAAO;4BACP,iBAAiB;;;;;;;;;;;;;;;;;YAOxB,MAAM,IAAI,KAAK,2CACd,8OAAC,qJAAA,CAAA,UAA2B;gBAAC,iBAAiB;;;;;;YAG/C,MAAM,IAAI,KAAK,4BACd,8OAAC,oIAAA,CAAA,oBAAiB;0BAChB,cAAA,8OAAC,wIAAA,CAAA,UAAS;;;;;;;;;;;;;;;;AAKpB;AAEe,SAAS;IACtB,qBACE,8OAAC,oIAAA,CAAA,oBAAiB;kBAChB,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}