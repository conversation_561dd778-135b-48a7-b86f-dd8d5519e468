[{"/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/app/layout.tsx": "1", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/app/page.tsx": "2", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ConnectionStatus.tsx": "3", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/EEGViewer/PlotlyChart.tsx": "4", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/EEGViewer/index.tsx": "5", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/FileUploadCard.tsx": "6", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ProgressIndicator.tsx": "7", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/contexts/WebSocketContext.tsx": "8", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/types/eeg.ts": "9", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/EEGViewer/ChannelGrid.tsx": "10", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/EEGViewer/ChannelRow.tsx": "11", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/ChannelSelectionPanel.tsx": "12", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/FrequencyFilterPanel.tsx": "13", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/HFOThresholdPanel.tsx": "14", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/MontageSelectionPanel.tsx": "15", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/SettingsManager.tsx": "16", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/TimeSegmentPanel.tsx": "17", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/ValidationDisplay.tsx": "18", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/index.tsx": "19", "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/contexts/ParameterContext.tsx": "20"}, {"size": 694, "mtime": 1754337371344, "results": "21", "hashOfConfig": "22"}, {"size": 5610, "mtime": 1754342801368, "results": "23", "hashOfConfig": "22"}, {"size": 3694, "mtime": 1754337763219, "results": "24", "hashOfConfig": "22"}, {"size": 6195, "mtime": 1754337646396, "results": "25", "hashOfConfig": "22"}, {"size": 10006, "mtime": 1754338221177, "results": "26", "hashOfConfig": "22"}, {"size": 3990, "mtime": 1754342793945, "results": "27", "hashOfConfig": "22"}, {"size": 2073, "mtime": 1754337743684, "results": "28", "hashOfConfig": "22"}, {"size": 2672, "mtime": 1754343060716, "results": "29", "hashOfConfig": "22"}, {"size": 4235, "mtime": 1754343033869, "results": "30", "hashOfConfig": "22"}, {"size": 2499, "mtime": 1754338197699, "results": "31", "hashOfConfig": "22"}, {"size": 3247, "mtime": 1754338169042, "results": "32", "hashOfConfig": "22"}, {"size": 12600, "mtime": 1754342654479, "results": "33", "hashOfConfig": "22"}, {"size": 5561, "mtime": 1754342568879, "results": "34", "hashOfConfig": "22"}, {"size": 4772, "mtime": 1754342524891, "results": "35", "hashOfConfig": "22"}, {"size": 4979, "mtime": 1754342544441, "results": "36", "hashOfConfig": "22"}, {"size": 8224, "mtime": 1754342708603, "results": "37", "hashOfConfig": "22"}, {"size": 11291, "mtime": 1754342604251, "results": "38", "hashOfConfig": "22"}, {"size": 5496, "mtime": 1754342679922, "results": "39", "hashOfConfig": "22"}, {"size": 5624, "mtime": 1754342503418, "results": "40", "hashOfConfig": "22"}, {"size": 9164, "mtime": 1754342462577, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "awjssk", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/app/layout.tsx", [], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/app/page.tsx", [], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ConnectionStatus.tsx", [], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/EEGViewer/PlotlyChart.tsx", ["102"], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/EEGViewer/index.tsx", ["103", "104"], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/FileUploadCard.tsx", ["105", "106", "107", "108", "109", "110", "111"], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ProgressIndicator.tsx", [], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/contexts/WebSocketContext.tsx", [], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/types/eeg.ts", [], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/EEGViewer/ChannelGrid.tsx", [], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/EEGViewer/ChannelRow.tsx", ["112", "113"], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/ChannelSelectionPanel.tsx", ["114", "115", "116", "117", "118", "119", "120", "121", "122", "123"], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/FrequencyFilterPanel.tsx", ["124"], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/HFOThresholdPanel.tsx", [], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/MontageSelectionPanel.tsx", [], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/SettingsManager.tsx", ["125", "126", "127", "128"], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/TimeSegmentPanel.tsx", ["129"], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/ValidationDisplay.tsx", [], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/components/ParameterConfiguration/index.tsx", ["130"], [], "/Users/<USER>/Work/biormika/biormika-http-backend/frontend/src/contexts/ParameterContext.tsx", [], [], {"ruleId": "131", "severity": 2, "message": "132", "line": 41, "column": 19, "nodeType": "133", "messageId": "134", "endLine": 41, "endColumn": 22, "suggestions": "135"}, {"ruleId": "136", "severity": 1, "message": "137", "line": 54, "column": 6, "nodeType": "138", "endLine": 54, "endColumn": 23, "suggestions": "139"}, {"ruleId": "140", "severity": 1, "message": "141", "line": 187, "column": 43, "nodeType": null, "messageId": "142", "endLine": 187, "endColumn": 48}, {"ruleId": "140", "severity": 1, "message": "143", "line": 4, "column": 10, "nodeType": null, "messageId": "142", "endLine": 4, "endColumn": 16}, {"ruleId": "140", "severity": 1, "message": "144", "line": 16, "column": 10, "nodeType": null, "messageId": "142", "endLine": 16, "endColumn": 20}, {"ruleId": "140", "severity": 1, "message": "145", "line": 18, "column": 9, "nodeType": null, "messageId": "142", "endLine": 18, "endColumn": 21}, {"ruleId": "140", "severity": 1, "message": "146", "line": 20, "column": 9, "nodeType": null, "messageId": "142", "endLine": 20, "endColumn": 23}, {"ruleId": "140", "severity": 1, "message": "147", "line": 25, "column": 9, "nodeType": null, "messageId": "142", "endLine": 25, "endColumn": 24}, {"ruleId": "140", "severity": 1, "message": "148", "line": 30, "column": 9, "nodeType": null, "messageId": "142", "endLine": 30, "endColumn": 19}, {"ruleId": "140", "severity": 1, "message": "149", "line": 95, "column": 24, "nodeType": null, "messageId": "142", "endLine": 95, "endColumn": 29}, {"ruleId": "131", "severity": 2, "message": "132", "line": 13, "column": 15, "nodeType": "133", "messageId": "134", "endLine": 13, "endColumn": 18, "suggestions": "150"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 37, "column": 19, "nodeType": "133", "messageId": "134", "endLine": 37, "endColumn": 22, "suggestions": "151"}, {"ruleId": "136", "severity": 1, "message": "152", "line": 57, "column": 6, "nodeType": "138", "endLine": 57, "endColumn": 40, "suggestions": "153"}, {"ruleId": "154", "severity": 2, "message": "155", "line": 62, "column": 9, "nodeType": "156", "messageId": "157", "endLine": 62, "endColumn": 24, "fix": "158"}, {"ruleId": "159", "severity": 2, "message": "160", "line": 228, "column": 16, "nodeType": "161", "messageId": "162", "suggestions": "163"}, {"ruleId": "159", "severity": 2, "message": "160", "line": 228, "column": 24, "nodeType": "161", "messageId": "162", "suggestions": "164"}, {"ruleId": "159", "severity": 2, "message": "160", "line": 307, "column": 33, "nodeType": "161", "messageId": "162", "suggestions": "165"}, {"ruleId": "159", "severity": 2, "message": "160", "line": 307, "column": 37, "nodeType": "161", "messageId": "162", "suggestions": "166"}, {"ruleId": "159", "severity": 2, "message": "160", "line": 307, "column": 58, "nodeType": "161", "messageId": "162", "suggestions": "167"}, {"ruleId": "159", "severity": 2, "message": "160", "line": 307, "column": 64, "nodeType": "161", "messageId": "162", "suggestions": "168"}, {"ruleId": "159", "severity": 2, "message": "160", "line": 307, "column": 90, "nodeType": "161", "messageId": "162", "suggestions": "169"}, {"ruleId": "159", "severity": 2, "message": "160", "line": 307, "column": 100, "nodeType": "161", "messageId": "162", "suggestions": "170"}, {"ruleId": "140", "severity": 1, "message": "171", "line": 32, "column": 9, "nodeType": null, "messageId": "142", "endLine": 32, "endColumn": 29}, {"ruleId": "140", "severity": 1, "message": "149", "line": 35, "column": 14, "nodeType": null, "messageId": "142", "endLine": 35, "endColumn": 19}, {"ruleId": "140", "severity": 1, "message": "149", "line": 56, "column": 14, "nodeType": null, "messageId": "142", "endLine": 56, "endColumn": 19}, {"ruleId": "140", "severity": 1, "message": "149", "line": 85, "column": 14, "nodeType": null, "messageId": "142", "endLine": 85, "endColumn": 19}, {"ruleId": "140", "severity": 1, "message": "149", "line": 106, "column": 20, "nodeType": null, "messageId": "142", "endLine": 106, "endColumn": 25}, {"ruleId": "140", "severity": 1, "message": "172", "line": 5, "column": 17, "nodeType": null, "messageId": "142", "endLine": 5, "endColumn": 25}, {"ruleId": "140", "severity": 1, "message": "173", "line": 5, "column": 37, "nodeType": null, "messageId": "142", "endLine": 5, "endColumn": 41}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["174", "175"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'channels' and 'selectedChannels.length'. Either include them or remove the dependency array.", "ArrayExpression", ["176"], "@typescript-eslint/no-unused-vars", "'index' is defined but never used.", "unusedVar", "'Upload' is defined but never used.", "'isDragging' is assigned a value but never used.", "'fileInputRef' is assigned a value but never used.", "'handleDragOver' is assigned a value but never used.", "'handleDragLeave' is assigned a value but never used.", "'handleDrop' is assigned a value but never used.", "'error' is defined but never used.", ["177", "178"], ["179", "180"], "React Hook useEffect has missing dependencies: 'expandedGroups' and 'groupNames'. Either include them or remove the dependency array. You can also do a functional update 'setExpandedGroups(e => ...)' if you only need 'expandedGroups' in the 'setExpandedGroups' call.", ["181"], "prefer-const", "'newContactSpecs' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "182", "text": "183"}, "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["184", "185", "186", "187"], ["188", "189", "190", "191"], ["192", "193", "194", "195"], ["196", "197", "198", "199"], ["200", "201", "202", "203"], ["204", "205", "206", "207"], ["208", "209", "210", "211"], ["212", "213", "214", "215"], "'isValidConfiguration' is assigned a value but never used.", "'Calendar' is defined but never used.", "'Save' is defined but never used.", {"messageId": "216", "fix": "217", "desc": "218"}, {"messageId": "219", "fix": "220", "desc": "221"}, {"desc": "222", "fix": "223"}, {"messageId": "216", "fix": "224", "desc": "218"}, {"messageId": "219", "fix": "225", "desc": "221"}, {"messageId": "216", "fix": "226", "desc": "218"}, {"messageId": "219", "fix": "227", "desc": "221"}, {"desc": "228", "fix": "229"}, [2143, 2213], "const newContactSpecs = { ...channel_selection.contact_specifications };", {"messageId": "230", "data": "231", "fix": "232", "desc": "233"}, {"messageId": "230", "data": "234", "fix": "235", "desc": "236"}, {"messageId": "230", "data": "237", "fix": "238", "desc": "239"}, {"messageId": "230", "data": "240", "fix": "241", "desc": "242"}, {"messageId": "230", "data": "243", "fix": "244", "desc": "233"}, {"messageId": "230", "data": "245", "fix": "246", "desc": "236"}, {"messageId": "230", "data": "247", "fix": "248", "desc": "239"}, {"messageId": "230", "data": "249", "fix": "250", "desc": "242"}, {"messageId": "230", "data": "251", "fix": "252", "desc": "233"}, {"messageId": "230", "data": "253", "fix": "254", "desc": "236"}, {"messageId": "230", "data": "255", "fix": "256", "desc": "239"}, {"messageId": "230", "data": "257", "fix": "258", "desc": "242"}, {"messageId": "230", "data": "259", "fix": "260", "desc": "233"}, {"messageId": "230", "data": "261", "fix": "262", "desc": "236"}, {"messageId": "230", "data": "263", "fix": "264", "desc": "239"}, {"messageId": "230", "data": "265", "fix": "266", "desc": "242"}, {"messageId": "230", "data": "267", "fix": "268", "desc": "233"}, {"messageId": "230", "data": "269", "fix": "270", "desc": "236"}, {"messageId": "230", "data": "271", "fix": "272", "desc": "239"}, {"messageId": "230", "data": "273", "fix": "274", "desc": "242"}, {"messageId": "230", "data": "275", "fix": "276", "desc": "233"}, {"messageId": "230", "data": "277", "fix": "278", "desc": "236"}, {"messageId": "230", "data": "279", "fix": "280", "desc": "239"}, {"messageId": "230", "data": "281", "fix": "282", "desc": "242"}, {"messageId": "230", "data": "283", "fix": "284", "desc": "233"}, {"messageId": "230", "data": "285", "fix": "286", "desc": "236"}, {"messageId": "230", "data": "287", "fix": "288", "desc": "239"}, {"messageId": "230", "data": "289", "fix": "290", "desc": "242"}, {"messageId": "230", "data": "291", "fix": "292", "desc": "233"}, {"messageId": "230", "data": "293", "fix": "294", "desc": "236"}, {"messageId": "230", "data": "295", "fix": "296", "desc": "239"}, {"messageId": "230", "data": "297", "fix": "298", "desc": "242"}, "suggestUnknown", {"range": "299", "text": "300"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "301", "text": "302"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [channels, channels.length, selectedChannels.length]", {"range": "303", "text": "304"}, {"range": "305", "text": "300"}, {"range": "306", "text": "302"}, {"range": "307", "text": "300"}, {"range": "308", "text": "302"}, "Update the dependencies array to be: [channel_selection.selected_leads, expandedGroups, groupNames]", {"range": "309", "text": "310"}, "replaceWithAlt", {"alt": "311"}, {"range": "312", "text": "313"}, "Replace with `&quot;`.", {"alt": "314"}, {"range": "315", "text": "316"}, "Replace with `&ldquo;`.", {"alt": "317"}, {"range": "318", "text": "319"}, "Replace with `&#34;`.", {"alt": "320"}, {"range": "321", "text": "322"}, "Replace with `&rdquo;`.", {"alt": "311"}, {"range": "323", "text": "324"}, {"alt": "314"}, {"range": "325", "text": "326"}, {"alt": "317"}, {"range": "327", "text": "328"}, {"alt": "320"}, {"range": "329", "text": "330"}, {"alt": "311"}, {"range": "331", "text": "332"}, {"alt": "314"}, {"range": "333", "text": "334"}, {"alt": "317"}, {"range": "335", "text": "336"}, {"alt": "320"}, {"range": "337", "text": "338"}, {"alt": "311"}, {"range": "339", "text": "340"}, {"alt": "314"}, {"range": "341", "text": "342"}, {"alt": "317"}, {"range": "343", "text": "344"}, {"alt": "320"}, {"range": "345", "text": "346"}, {"alt": "311"}, {"range": "347", "text": "348"}, {"alt": "314"}, {"range": "349", "text": "350"}, {"alt": "317"}, {"range": "351", "text": "352"}, {"alt": "320"}, {"range": "353", "text": "354"}, {"alt": "311"}, {"range": "355", "text": "356"}, {"alt": "314"}, {"range": "357", "text": "358"}, {"alt": "317"}, {"range": "359", "text": "360"}, {"alt": "320"}, {"range": "361", "text": "362"}, {"alt": "311"}, {"range": "363", "text": "364"}, {"alt": "314"}, {"range": "365", "text": "366"}, {"alt": "317"}, {"range": "367", "text": "368"}, {"alt": "320"}, {"range": "369", "text": "370"}, {"alt": "311"}, {"range": "371", "text": "372"}, {"alt": "314"}, {"range": "373", "text": "374"}, {"alt": "317"}, {"range": "375", "text": "376"}, {"alt": "320"}, {"range": "377", "text": "378"}, [1057, 1060], "unknown", [1057, 1060], "never", [1917, 1934], "[channels, channels.length, selectedChannels.length]", [290, 293], [290, 293], [911, 914], [911, 914], [1943, 1977], "[channel_selection.selected_leads, expandedGroups, groupNames]", "&quot;", [7624, 7775], "\r\n        Enter individual contact numbers separated by comma or range separated by dash \r\n        (e.g., &quot;1-5,7,9\" for contacts 1,2,3,4,5,7,9)\r\n      ", "&ldquo;", [7624, 7775], "\r\n        Enter individual contact numbers separated by comma or range separated by dash \r\n        (e.g., &ldquo;1-5,7,9\" for contacts 1,2,3,4,5,7,9)\r\n      ", "&#34;", [7624, 7775], "\r\n        Enter individual contact numbers separated by comma or range separated by dash \r\n        (e.g., &#34;1-5,7,9\" for contacts 1,2,3,4,5,7,9)\r\n      ", "&rdquo;", [7624, 7775], "\r\n        Enter individual contact numbers separated by comma or range separated by dash \r\n        (e.g., &rdquo;1-5,7,9\" for contacts 1,2,3,4,5,7,9)\r\n      ", [7624, 7775], "\r\n        Enter individual contact numbers separated by comma or range separated by dash \r\n        (e.g., \"1-5,7,9&quot; for contacts 1,2,3,4,5,7,9)\r\n      ", [7624, 7775], "\r\n        Enter individual contact numbers separated by comma or range separated by dash \r\n        (e.g., \"1-5,7,9&ldquo; for contacts 1,2,3,4,5,7,9)\r\n      ", [7624, 7775], "\r\n        Enter individual contact numbers separated by comma or range separated by dash \r\n        (e.g., \"1-5,7,9&#34; for contacts 1,2,3,4,5,7,9)\r\n      ", [7624, 7775], "\r\n        Enter individual contact numbers separated by comma or range separated by dash \r\n        (e.g., \"1-5,7,9&rdquo; for contacts 1,2,3,4,5,7,9)\r\n      ", [11819, 11973], "\r\n                      Examples: &quot;1-5\" (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: &ldquo;1-5\" (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: &#34;1-5\" (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: &rdquo;1-5\" (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5&quot; (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5&ldquo; (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5&#34; (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5&rdquo; (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), &quot;1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), &ldquo;1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), &#34;1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), &rdquo;1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), \"1,3,5&quot; (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), \"1,3,5&ldquo; (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), \"1,3,5&#34; (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), \"1,3,5&rdquo; (contacts 1, 3, and 5), \"1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), &quot;1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), &ldquo;1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), &#34;1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), &rdquo;1-3,5,7-9\" (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9&quot; (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9&ldquo; (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9&#34; (mixed ranges and individual)\r\n                    ", [11819, 11973], "\r\n                      Examples: \"1-5\" (contacts 1 to 5), \"1,3,5\" (contacts 1, 3, and 5), \"1-3,5,7-9&rdquo; (mixed ranges and individual)\r\n                    "]