"use client";

import React, { useState, useRef, DragEvent } from "react";
import { Upload, FileText, X, AlertCircle } from "lucide-react";
import clsx from "clsx";

interface FileUploadCardProps {
  onFileSelect: (filepath: string) => void;
  onStartAnalysis: () => void;
  error?: string;
}

const FileUploadCard: React.FC<FileUploadCardProps> = ({ onFileSelect, onStartAnalysis, error }) => {
  const [filepath, setFilepath] = useState("");
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.name.endsWith(".edf")) {
        setFilepath(file.name);
        onFileSelect(file.name);
      }
    }
  };

  const handleInputChange = (value: string) => {
    setFilepath(value);
    onFileSelect(value);
  };

  const clearInput = () => {
    setFilepath("");
    onFileSelect("");
  };

  return (
    <div className="card-soft">
      <div className="p-6">
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">File path</label>
          <div className="relative">
            <input
              type="text"
              value={filepath}
              onChange={(e) => handleInputChange(e.target.value)}
              placeholder="/path/to/file.edf"
              className="input-modern"
            />
            {filepath && (
              <button onClick={clearInput} className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600">
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {error && (
          <div className="mt-4 p-3 rounded bg-red-50 border border-red-200 flex items-start gap-2">
            <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}

        {filepath && (
          <div className="mt-3 p-2 rounded bg-gray-50 border border-gray-200 flex items-center gap-2">
            <FileText className="w-4 h-4 text-gray-600" />
            <span className="text-sm text-gray-700 truncate">{filepath}</span>
          </div>
        )}

        <button
          onClick={onStartAnalysis}
          disabled={!filepath}
          className={clsx("gradient-button w-full mt-4", !filepath && "opacity-50 cursor-not-allowed")}
        >
          Start Analysis
        </button>
      </div>
    </div>
  );
};

export default FileUploadCard;
