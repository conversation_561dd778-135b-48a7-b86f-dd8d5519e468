"use client";

import { useState } from "react";
import { Activity } from "lucide-react";
import EEGViewer from "@/components/EEGViewer";
import FileUploadCard from "@/components/FileUploadCard";
import { WebSocketProvider } from "@/contexts/WebSocketContext";

export default function Home() {
  const [filepath, setFilepath] = useState("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState("");

  const startAnalysis = async () => {
    if (!filepath) {
      setError("Please enter a file path");
      return;
    }

    try {
      setError("");
      const response = await fetch("http://localhost:8000/api/analyze", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ filepath }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to start analysis");
      }

      setIsAnalyzing(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to start analysis");
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="min-h-screen min-w-screen bg-white overflow-hidden">
      {!isAnalyzing ? (
        <>
          <main className=" mx-auto px-6 py-8 min-h-screen min-w-screen">
            <FileUploadCard onFileSelect={setFilepath} onStartAnalysis={startAnalysis} error={error} />
          </main>
        </>
      ) : (
        <WebSocketProvider>
          <EEGViewer />
        </WebSocketProvider>
      )}
    </div>
  );
}
