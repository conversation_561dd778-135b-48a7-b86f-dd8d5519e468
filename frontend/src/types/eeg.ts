export interface ThresholdParameters {
  amplitude1: number;
  amplitude2: number;
  peaks1: number;
  peaks2: number;
  duration: number;
  temporal_sync: number;
  spatial_sync: number;
}

export interface MontageConfig {
  type: 'bipolar' | 'average' | 'referential';
  reference_channels?: string[];
}

export interface FrequencyFilter {
  low_cutoff: number;
  high_cutoff: number;
}

export interface TimeSegment {
  entire_file: boolean;
  start_date?: string;
  start_time?: string;
  end_date?: string;
  end_time?: string;
  duration_seconds?: number;
}

export interface AnalysisParameters {
  thresholds: ThresholdParameters;
  montage: MontageConfig;
  frequency: FrequencyFilter;
  time_segment: TimeSegment;
  selected_channels: string[];
}

export interface FileInfo {
  file_id: string;
  filename: string;
  start_date: string;
  start_time: string;
  end_date: string;
  end_time: string;
  sampling_rate: number;
  channels: string[];
  duration_seconds: number;
}

export interface JobStatus {
  job_id: string;
  file_id: string;
  status: 'queued' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  created_at: string;
  updated_at: string;
}

export interface HFOEvent {
  channel: string;
  start_time: number;
  end_time: number;
  amplitude: number;
  frequency: number;
}

export interface ChunkResult {
  type: string;
  time_range: [number, number];
  hfo_events: HFOEvent[];
  channel_data: Record<string, number[]>;
  progress: number;
  chunk_number: number;
  total_chunks: number;
}

export interface WebSocketMessage {
  type: 'preview_ready' | 'chunk_complete' | 'hfo_detected' | 'analysis_complete' | 'error' | 'status_update';
  data?: any;
  message?: string;
  status?: string;
  progress?: number;
}