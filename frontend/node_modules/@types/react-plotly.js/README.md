# Installation
> `npm install --save @types/react-plotly.js`

# Summary
This package contains type definitions for react-plotly.js (https://github.com/plotly/react-plotly.js#readme).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-plotly.js.

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/plotly.js](https://npmjs.com/package/@types/plotly.js), [@types/react](https://npmjs.com/package/@types/react)

# Credits
These definitions were written by [<PERSON>](https://github.com/jon<PERSON>dman), [<PERSON>](https://github.com/gricey432), and [<PERSON>](https://github.com/phuebner).
