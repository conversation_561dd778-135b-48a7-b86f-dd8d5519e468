from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import os
import sys
import json
import asyncio
import numpy as np
import logging
from typing import Dict, List, Any, Optional
import traceback
import uvicorn

sys.path.append('./core/hfo_engine')

from services.analysis_service import AnalysisService
from core.validators import EDFValidator, ParameterValidator, SignalValidator
from core.exceptions.error_handlers import setup_exception_handlers
from core.exceptions.validation_exceptions import ValidationError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Biormika HFO Detector MVP", version="1.0.0")

# Setup exception handlers
setup_exception_handlers(app)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

analysis_service = AnalysisService()
active_websocket = None
current_filepath = None

# Initialize validators
edf_validator = EDFValidator()
parameter_validator = ParameterValidator()
signal_validator = SignalValidator()

class FileAnalyzeRequest(BaseModel):
    filepath: str
    
class StartAnalysisRequest(BaseModel):
    filepath: str
    parameters: Optional[Dict[str, Any]] = None

@app.get("/")
async def root():
    return {"message": "Biormika HFO Detector MVP", "version": "1.0.0"}

@app.get("/api/parameters/options")
async def get_parameter_options():
    """Get all available parameter options for the UI"""
    from models.parameters import FrequencyFilter
    
    return {
        "thresholds": {
            "amplitude1": {"min": 2, "max": 5, "default": 2, "description": "HFO amp >= energy signal by (times of std)"},
            "amplitude2": {"min": 2, "max": 5, "default": 2, "description": "HFO amp >= mean baseline signal by (times of std)"},
            "peaks1": {"min": 2, "max": 8, "default": 6, "description": "Number of peaks in HFO >= Amplitude 1"},
            "peaks2": {"min": 2, "max": 6, "default": 3, "description": "Number of peaks in HFO >= Amplitude 2"},
            "duration": {"min": 5, "max": 15, "default": 10, "description": "HFO length >= (ms)"},
            "temporal_sync": {"min": 5, "max": 12, "default": 10, "description": "Inter HFO interval in any channel <= (ms)"},
            "spatial_sync": {"min": 5, "max": 12, "default": 10, "description": "Inter HFO interval across channels <= (ms)"}
        },
        "montage": {
            "types": ["bipolar", "average", "referential"],
            "default": "bipolar"
        },
        "frequency": {
            "low_cutoff_options": FrequencyFilter.LOW_CUTOFF_OPTIONS,
            "high_cutoff_options": FrequencyFilter.HIGH_CUTOFF_OPTIONS,
            "default_low": 50,
            "default_high": 300
        },
        "time_segment": {
            "modes": ["entire_file", "start_end_times", "start_time_duration"],
            "default": "entire_file"
        }
    }

@app.post("/api/analyze")
async def analyze(request: FileAnalyzeRequest):
    """Start analysis of a local EDF file with comprehensive validation"""
    global current_filepath
    
    try:
        # Validate file existence and format
        is_valid, errors = edf_validator.validate_file(request.filepath)
        if not is_valid:
            logger.error(f"EDF file validation failed: {errors}")
            raise ValidationError(
                message="EDF file validation failed",
                errors=errors,
                field="filepath"
            )
        
        # Load and validate EDF header
        from browse_files import browse_edf
        try:
            header = browse_edf(request.filepath)
            is_valid, errors = edf_validator.validate_header(header)
            if not is_valid:
                logger.error(f"EDF header validation failed: {errors}")
                raise ValidationError(
                    message="EDF header validation failed",
                    errors=errors
                )
        except Exception as e:
            logger.error(f"Failed to read EDF file: {str(e)}")
            raise ValidationError(
                message="Failed to read EDF file",
                errors=[str(e)]
            )
        
        current_filepath = request.filepath
        
        # Extract key information from header for response
        if isinstance(header['frequency'], np.ndarray):
            sampling_rate = float(header['frequency'][0])
        elif isinstance(header['frequency'], list):
            sampling_rate = float(header['frequency'][0])
        else:
            sampling_rate = float(header['frequency'])
        
        return {
            "status": "ready",
            "message": "File validated successfully. Connect to WebSocket to start streaming.",
            "filepath": request.filepath,
            "file_info": {
                "channels": header['label'],
                "sampling_rate": sampling_rate,
                "duration_seconds": header['records'] * header['duration'],
                "start_date": header['startdate'],
                "start_time": header['starttime']
            },
            "validation_warnings": edf_validator.warnings if edf_validator.warnings else None
        }
        
    except ValidationError:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in analyze endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/analyze/start")
async def start_analysis(request: StartAnalysisRequest):
    """Start analysis with full parameter configuration"""
    global current_filepath, current_parameters
    
    try:
        # Validate file
        is_valid, errors = edf_validator.validate_file(request.filepath)
        if not is_valid:
            raise ValidationError(
                message="EDF file validation failed",
                errors=errors,
                field="filepath"
            )
        
        # Load header for validation context
        from browse_files import browse_edf
        header = browse_edf(request.filepath)
        
        # Validate header
        is_valid, errors = edf_validator.validate_header(header)
        if not is_valid:
            raise ValidationError(
                message="EDF header validation failed",
                errors=errors
            )
        
        # Extract sampling rate
        if isinstance(header['frequency'], np.ndarray):
            sampling_rate = float(np.mean(header['frequency']))
        elif isinstance(header['frequency'], list):
            sampling_rate = float(np.mean(header['frequency']))
        else:
            sampling_rate = float(header['frequency'])
        
        # Prepare parameters with defaults if not provided
        if request.parameters:
            params = request.parameters
        else:
            params = {
                "thresholds": {},
                "montage": {"type": "bipolar"},
                "frequency": {"low_cutoff": 50, "high_cutoff": 300},
                "time_segment": {"mode": "entire_file"},
                "channel_selection": {"selected_leads": [], "contact_specifications": {}}
            }
        
        # Validate frequency against sampling rate
        if params.get("frequency"):
            high_cutoff = params["frequency"].get("high_cutoff", 300)
            max_freq = sampling_rate / 3  # Original code constraint
            
            if high_cutoff > max_freq:
                raise ValidationError(
                    message=f"High cutoff frequency ({high_cutoff}Hz) exceeds maximum usable frequency ({max_freq:.1f}Hz) for sampling rate {sampling_rate}Hz",
                    errors=[f"Maximum frequency should be sampling_rate/3 = {max_freq:.1f}Hz"],
                    field="frequency.high_cutoff"
                )
        
        # Validate time segment
        if params.get("time_segment"):
            time_seg = params["time_segment"]
            file_duration = header['records'] * header['duration']
            
            # Parse and validate time segment based on mode
            if time_seg.get("mode") == "start_end_times":
                if not all([time_seg.get("start_date"), time_seg.get("start_time"),
                           time_seg.get("end_date"), time_seg.get("end_time")]):
                    raise ValidationError(
                        message="Start/end times mode requires all date/time fields",
                        errors=["Missing required date/time fields"],
                        field="time_segment"
                    )
            elif time_seg.get("mode") == "start_time_duration":
                if not all([time_seg.get("start_date"), time_seg.get("start_time"),
                           time_seg.get("duration_seconds")]):
                    raise ValidationError(
                        message="Start time/duration mode requires start date/time and duration",
                        errors=["Missing required fields"],
                        field="time_segment"
                    )
                if time_seg.get("duration_seconds", 0) > file_duration:
                    raise ValidationError(
                        message=f"Duration ({time_seg['duration_seconds']}s) exceeds file duration ({file_duration}s)",
                        errors=["Duration too long"],
                        field="time_segment.duration_seconds"
                    )
        
        # Validate montage
        if params.get("montage"):
            montage = params["montage"]
            if montage.get("type") == "referential" and not montage.get("reference_channel"):
                raise ValidationError(
                    message="Referential montage requires a reference channel",
                    errors=["Missing reference channel"],
                    field="montage.reference_channel"
                )
            
            if montage.get("type") == "bipolar" and len(header['label']) < 2:
                raise ValidationError(
                    message="Bipolar montage requires at least 2 channels",
                    errors=[f"Only {len(header['label'])} channel(s) available"],
                    field="montage.type"
                )
        
        # Store validated parameters
        current_filepath = request.filepath
        current_parameters = params
        
        # Return success with processed channel information
        channel_groups = _extract_channel_groups(header['label'])
        
        return {
            "status": "ready",
            "message": "Analysis parameters validated. Ready to start streaming.",
            "filepath": request.filepath,
            "file_info": {
                "channels": header['label'],
                "channel_groups": channel_groups,
                "sampling_rate": sampling_rate,
                "max_frequency": sampling_rate / 3,
                "duration_seconds": header['records'] * header['duration'],
                "start_date": header['startdate'],
                "start_time": header['starttime']
            },
            "validated_parameters": params
        }
        
    except ValidationError:
        raise
    except Exception as e:
        logger.error(f"Error in start_analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def _extract_channel_groups(labels: List[str]) -> Dict[str, List[int]]:
    """Extract channel groups from labels (e.g., 'FP1', 'FP2' -> group 'FP' with contacts [1,2])"""
    import re
    from collections import defaultdict
    
    groups = defaultdict(list)
    for label in labels:
        # Try to extract base name and number
        match = re.match(r'^(?:POL |P )?(\w+?)(\d+)$', label)
        if match:
            group_name, contact_num = match.groups()
            groups[group_name].append(int(contact_num))
        else:
            # No number - treat as standalone channel
            clean_label = re.sub(r'^(POL |P )', '', label)
            if clean_label:
                groups[clean_label] = []
    
    # Sort contact numbers for each group
    for group in groups:
        if groups[group]:  # Only sort if there are numbers
            groups[group] = sorted(groups[group])
    
    return dict(groups)

# Global to store parameters
current_parameters = None

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time EEG data streaming"""
    global active_websocket, current_filepath
    
    await websocket.accept()
    active_websocket = websocket
    
    try:
        if not current_filepath:
            await websocket.send_json({
                "type": "error",
                "message": "No file selected. Please call /api/analyze first."
            })
            return
        
        await websocket.send_json({
            "type": "status",
            "message": "Starting analysis..."
        })
        
        default_params = {
            "thresholds": {
                "amplitude1": 2,
                "amplitude2": 2,
                "peaks1": 6,
                "peaks2": 3,
                "duration": 10,
                "temporal_sync": 10,
                "spatial_sync": 10
            },
            "montage": {"type": "bipolar"},
            "frequency": {"low_cutoff": 50, "high_cutoff": 300},
            "time_segment": {"mode": "entire_file"},
            "channel_selection": {"selected_leads": [], "contact_specifications": {}}
        }
        
        from models.parameters import AnalysisParameters
        parameters = AnalysisParameters(**default_params)
        
        # Validate parameters before processing
        # Get header info for validation context
        from browse_files import browse_edf
        from pyedfreader import edfread
        header = browse_edf(current_filepath)
        _, record_data = edfread(current_filepath)
        
        # Extract sampling rate for validation
        if isinstance(header['frequency'], np.ndarray):
            sampling_rate = float(header['frequency'][0])
        elif isinstance(header['frequency'], list):
            sampling_rate = float(header['frequency'][0])
        else:
            sampling_rate = float(header['frequency'])
        
        # Validate all parameters
        validation_data = {
            "thresholds": parameters.thresholds.model_dump(),
            "frequency": parameters.frequency.model_dump(),
            "montage": parameters.montage.model_dump(),
            "time_segment": parameters.time_segment.model_dump(),
            "channel_selection": parameters.channel_selection.model_dump(),
            "available_channels": header['label'],
            "sampling_rate": sampling_rate,
            "file_duration": header['records'] * header['duration']
        }
        
        is_valid, param_errors = parameter_validator.validate(validation_data)
        if not is_valid:
            await websocket.send_json({
                "type": "validation_error",
                "message": "Invalid analysis parameters",
                "errors": param_errors
            })
            logger.error(f"Parameter validation failed: {param_errors}")
            return
        
        # Send validation warnings if any
        if parameter_validator.warnings:
            await websocket.send_json({
                "type": "validation_warning",
                "warnings": parameter_validator.warnings
            })
        
        processor = await analysis_service.create_processor(current_filepath, parameters)
        
        preview_result = await processor.process_preview()
        await websocket.send_json({
            "type": "preview",
            "data": preview_result
        })
        
        total_chunks = processor.get_total_chunks()
        
        for chunk_num in range(total_chunks):
            chunk_result = await processor.process_chunk(chunk_num)
            
            progress = ((chunk_num + 1) / total_chunks) * 100
            
            await websocket.send_json({
                "type": "chunk",
                "data": {
                    "chunk_number": chunk_num,
                    "total_chunks": total_chunks,
                    "time_range": chunk_result["time_range"],
                    "hfo_events": chunk_result.get("hfo_events", []),
                    "channel_data": chunk_result.get("channel_data", {}),
                    "progress": progress
                }
            })
            
            await asyncio.sleep(0.01)
        
        await websocket.send_json({
            "type": "complete",
            "data": {
                "total_hfos": processor.get_total_hfos(),
                "summary": processor.get_summary()
            }
        })
        
    except WebSocketDisconnect:
        active_websocket = None
    except Exception as e:
        await websocket.send_json({
            "type": "error",
            "message": str(e),
            "traceback": traceback.format_exc()
        })
        active_websocket = None

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)